import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { ApplicationE } from '@consts/company/job/application';
import { FastifyStateI } from '@interfaces/common/declaration';

import { Application } from '@schemas/company/job/application';
import { isNullUndefined } from '@utils/data/data';
import { EntityMemberModule } from '../entityMember';
import { CursorDataI, IdLabelCountDataTypeI, IdLabelI, NumberNullI } from '@interfaces/common/data';
import { Prisma } from '@prisma/postgres';
import User from '@modules/user';
import { DesignationModule } from '../designation';
import { EntityModule } from '../entity';
import { JobUtil } from '@utils/modules/company/job/job';
import { NumberUtil } from '@utils/data/number';
import { ApplicantFilterForEntityMemberParamsI, JobFiltersParamsI } from '@interfaces/company/job';

export const ApplicationModule = {
  fetchManyForApplicant: async (
    state: FastifyStateI,
    { cursorId, pageSize, status }: Application.FetchManyForApplicantQueryI,
    params: Application.FetchManyForApplicantBodyI,
  ): Promise<CursorDataI<Application.FetchManyForApplicantResultI>> => {
    const selfProfileId = state.profileId;
    const where: Prisma.JobApplicationWhereInput = {
      applicantId: selfProfileId,
    };

    if (cursorId) {
      where.cursorId = {
        lt: cursorId,
      };
    }

    if (status) {
      where.status = status;
    }

if (params?.countries?.length) {
  const jobCountryFilter = {
    countryIso2: {
      in: params.countries
    }
  };

  if (where.AND) {
    if (Array.isArray(where.AND)) {
      where.AND.push({ Job: jobCountryFilter });
    } else {
      where.AND = [where.AND, { Job: jobCountryFilter }];
    }
  } else {
    where.AND = [{ Job: jobCountryFilter }];
  }
}

    if (params.designations?.length) {
      const designationConditions: Prisma.JobApplicationWhereInput[] = [];
      const designationAlternativeIds = params.designations.filter((d) => d.dataType === 'master').map((d) => d.id);
      const designationRawDataIds = params.designations.filter((d) => d.dataType === 'raw').map((d) => d.id);

      if (designationAlternativeIds.length) {
        designationConditions.push({
          Job: {
            designationAlternativeId: {
              in: designationAlternativeIds,
            },
          },
        });
      }

      if (designationRawDataIds.length) {
        designationConditions.push({
          Job: {
            designationRawDataId: {
              in: designationRawDataIds,
            },
          },
        });
      }
      if (where.OR) {
        where.OR = [...(Array.isArray(where.OR) ? where.OR : [where.OR]), ...designationConditions];
      } else {
        where.OR = designationConditions;
      }
    }

    if (params?.shipTypes?.length) {
  const shipTypeIds = params.shipTypes.filter(ship => ship.dataType === 'master').map(item => item.id);
  const shipTypeRawDataIds = params.shipTypes.filter(ship => ship.dataType === 'raw').map(item => item.id);

  const shipTypeConditions: Prisma.JobApplicationWhereInput[] = [];

  if (shipTypeIds.length > 0) {
    shipTypeConditions.push({
      Job: {
        shipTypeId: {
          in: shipTypeIds
        }
      }
    });
  }

  if (shipTypeRawDataIds.length > 0) {
    shipTypeConditions.push({
      Job: {
        shipTypeRawDataId: {
          in: shipTypeRawDataIds
        }
      }
    });
  }

  if (shipTypeConditions.length > 0) {
    if (where.OR) {
      where.OR = [...(Array.isArray(where.OR) ? where.OR : [where.OR]), ...shipTypeConditions];
    } else {
      where.OR = shipTypeConditions;
    }
  }
}
    if (params?.internetLimits?.length) {
      const internetConditions: Prisma.JobApplicationWhereInput[] = [];

      params.internetLimits.forEach((limit) => {
        switch (limit.id) {
          case '0':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  OR: [
                    { internetAvailable: false },
                    { internetAvailable: null }
                  ]
                }
              }
            });
            break;
          case '<200':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  internetAvailable: true,
                  internetLimitPerDay: { lt: 200 }
                }
              }
            });
            break;
          case '200-500':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  internetAvailable: true,
                  internetLimitPerDay: { gte: 200, lte: 500 }
                }
              }
            });
            break;
          case '500-1000':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  internetAvailable: true,
                  internetLimitPerDay: { gte: 501, lte: 1000 }
                }
              }
            });
            break;
          case '1000+':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  internetAvailable: true,
                  internetLimitPerDay: { gt: 1000 }
                }
              }
            });
            break;
          case 'unknown':
            internetConditions.push({
              Job: {
                JobBenefit: {
                  internetAvailable: null,
                  internetLimitPerDay: null
                }
              }
            });
            break;
        }
      });

      if (internetConditions.length > 0) {
        const internetFilter = { OR: internetConditions };

        if (where.AND) {
          if (Array.isArray(where.AND)) {
            where.AND.push(internetFilter);
          } else {
            where.AND = [where.AND, internetFilter];
          }
        } else {
          where.AND = [internetFilter];
        }
      }
    }

    const jobApplications = await prismaPG.jobApplication.findMany({
      select: {
        id: true,
        cursorId: true,
        status: true,
        matching: true,
        Entity: {
          select: {
            id: true,
            name: true,
          },
        },
        EntityRawData: {
          select: {
            id: true,
            name: true,
          },
        },
        Job: {
          select: {
            DesignationAlternative: {
              select: {
                id: true,
                name: true,
              },
            },
            DesignationRawData: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      where,
      take: pageSize + 1,
      orderBy: {
        updatedAt: 'desc',
      },
    });
    const data: Application.FetchManyForApplicantResultI[] = [];

    if (jobApplications?.length) {
      data.push(
        ...(jobApplications.slice(0, pageSize) ?? []).map(
          ({
            cursorId,
            Entity,
            EntityRawData,
            id,
            Job: { DesignationAlternative, DesignationRawData },
            matching,
            status,
          }) =>
            ({
              cursorId: cursorId.toString(),
              designation: DesignationModule.transformObj({ DesignationAlternative, DesignationRawData }),
              entity: EntityModule.transformObj({ Entity, EntityRawData }),
              id,
              matching,
              status,
            }) as unknown as Application.FetchManyForApplicantResultI,
        ),
      );
    }
    const nextCursorId: NumberNullI = data.length ? Number(data[data.length - 1].cursorId) : null;
    return {
      data,
      nextCursorId,
    };
  },
  fetchManyForEntityMember: async (
    state: FastifyStateI,
    { cursorId, jobId, pageSize, status }: Application.FetchManyForEntityMemberQueryI,
    params: Application.FetchManyForEntityMemberBodyI,
  ): Promise<CursorDataI<Application.FetchManyForEntityMemberResultI>> => {
    const selfProfileId = state.profileId;
    const jobType = await prismaPG.job.findUnique({
      select: {
        jobType: true,
      },
      where: {
        id: jobId,
      },
    });
    const where: Prisma.JobApplicationWhereInput = {
      jobId,
      NOT: {
        ApplicantProfile: {
          OR: [
            {
              BlockedByProfile: {
                some: {
                  blockerId: selfProfileId,
                },
              },
            },
            {
              BlockedProfile: {
                some: {
                  blockedId: selfProfileId,
                },
              },
            },
          ],
        },
      },
    };
    if (cursorId) {
      where.cursorId = {
        lt: cursorId,
      };
    }
    if (status) {
      where.status = status;
    }
    if (params.countries?.length) {
      const applicantCountryFilter = {
        countryIso2: {
          in: params.countries,
        },
      };

      if (where.AND) {
        if (Array.isArray(where.AND)) {
          where.AND.push({ ApplicantProfile: applicantCountryFilter });
        } else {
          where.AND = [where.AND, { ApplicantProfile: applicantCountryFilter }];
        }
      } else {
        where.AND = [{ ApplicantProfile: applicantCountryFilter }];
      }
    }
    if (params.designations?.length) {
      const designationConditions: Prisma.JobApplicationWhereInput[] = [
        {
          ApplicantProfile: {
            designationAlternativeId: {
              in: params.designations.filter(d => d.dataType === 'master').map(d => d.id),
            },
          },
        },
        {
          ApplicantProfile: {
            designationRawDataId: {
              in: params.designations.filter(d => d.dataType === 'raw').map(d => d.id),
            },
          },
        },
      ];

      if (where.OR) {
        where.OR = [...where.OR, ...designationConditions];
      } else {
        where.OR = designationConditions;
      }
    }

    if (params.yearsOfExperiences?.length) {
      const experienceRanges = params.yearsOfExperiences.map((range) => range.id);
      const yearsField = jobType?.jobType === 'SAILING' ? 'e."sailingYears"' : 'e."years"';
      const monthsField = jobType?.jobType === 'SAILING' ? 'e."sailingMonths"' : 'e."months"';
      const applicationIds = await prismaPG.$queryRaw<IdLabelI[]>`
        SELECT ja.id::text
        FROM company."JobApplication" ja
        INNER JOIN "user"."Profile" p ON ja."applicantId" = p.id
        INNER JOIN career."Experience" e ON p.id = e."profileId"
        WHERE ja."jobId" = ${jobId}::uuid
        AND (
          CASE
            WHEN ${Prisma.raw(yearsField)} + ${Prisma.raw(monthsField)}/12.0 < 1 THEN '<1'
            WHEN ${Prisma.raw(yearsField)} + ${Prisma.raw(monthsField)}/12.0 BETWEEN 1 AND 3 THEN '1-3'
            WHEN ${Prisma.raw(yearsField)} + ${Prisma.raw(monthsField)}/12.0 BETWEEN 3 AND 5 THEN '3-5'
            WHEN ${Prisma.raw(yearsField)} + ${Prisma.raw(monthsField)}/12.0 BETWEEN 5 AND 10 THEN '5-10'
            WHEN ${Prisma.raw(yearsField)} + ${Prisma.raw(monthsField)}/12.0 > 10 THEN '10+'
            ELSE 'Unknown'
          END
        ) IN (${Prisma.join(experienceRanges)})
      `;
      where.id = { in: applicationIds.map((a) => a.id) };
    }

    const jobApplications = await prismaPG.jobApplication.findMany({
      select: {
        id: true,
        cursorId: true,
        status: true,
        matching: true,
        createdAt: true,
        DecisionMakerProfile: {
          select: {
            id: true,
            avatar: true,
            name: true,
            designationText: true,
            designationAlternativeId: true,
            designationRawDataId: true,
            entityText: true,
            entityId: true,
            entityRawDataId: true,
            status: true,
          },
        },
        ApplicantProfile: {
          select: {
            id: true,
            avatar: true,
            name: true,
            designationText: true,
            designationAlternativeId: true,
            designationRawDataId: true,
            entityText: true,
            entityId: true,
            entityRawDataId: true,
            status: true,
          },
        },
      },
      where,
      take: pageSize + 1,
      orderBy: {
        updatedAt: 'desc',
      },
    });
    const data: Application.FetchManyForEntityMemberResultI[] = [];

    if (jobApplications?.length) {
      data.push(
        ...(jobApplications.slice(0, pageSize) ?? []).map(
          ({ ApplicantProfile, cursorId, DecisionMakerProfile, id, matching, status, createdAt }) =>
            ({
              cursorId: cursorId.toString(),
              id,
              matching,
              status,
              ApplicantProfile: User.ProfileModule.transformProfile(ApplicantProfile),
              DecisionMakerProfile: User.ProfileModule.transformProfile(DecisionMakerProfile),
              createdAt,
            }) as unknown as Application.FetchManyForEntityMemberResultI,
        ),
      );
    }
    const nextCursorId: NumberNullI = data.length ? Number(data[data.length - 1].cursorId) : null;
    return {
      data,
      nextCursorId,
    };
  },
  upsertOneForApplicant: async (
    state: FastifyStateI,
    { applicationId, jobId, status: requestedStatus }: Application.UpsertOneForApplicantI,
  ): Promise<Pick<Application.Application, 'id'> | { redirectTo: 'email' | 'external', email?: string, url?: string }> => {
    const selfProfileId = state.profileId;

    const job = await prismaPG.job.findUnique({
      where: { id: jobId },
      select: {
        applicationMethod: true,
        applicationEmail: true,
        applicationUrl: true,
        entityId:true,
        entityRawDataId:true,
        profileId:true
      }
    });
    if (!job) {
      throw new AppError('JOB008');
    }
    if (job.applicationMethod === 'EMAIL') {
      return {
        redirectTo: 'email',
        email: job.applicationEmail
      };
    }
    if (job.applicationMethod === 'EXTERNAL_LINK') {
      return {
        redirectTo: 'external',
        url: job.applicationUrl
      };
    }
    const existingApplication = await prismaPG.jobApplication.findUnique({
      select: { status: true },
      where: applicationId
        ? { id: applicationId }
        : {
            applicantId_jobId: {
              applicantId: selfProfileId,
              jobId,
            },
          },
    });
    let application: Pick<Application.Application, 'id'>;
    if (existingApplication) {
      if (existingApplication.status === requestedStatus) {
        throw new AppError('APPL003');
      }
      if (
        (requestedStatus === 'PENDING' &&
          [
            isNullUndefined(existingApplication),
            (['WITHDREW', 'REJECTED_BY_APPLICANT'] as ApplicationE.StatusI[]).includes(existingApplication.status),
          ].includes(true)) ||
        (requestedStatus === 'WITHDREW' &&
          !(['PENDING', 'SHORTLISTED', 'OFFERED', 'ACCEPTED_BY_ENTITY'] as ApplicationE.StatusI[]).includes(
            existingApplication.status as ApplicationE.StatusI,
          )) ||
        (requestedStatus === 'ACCEPTED_BY_APPLICANT' && existingApplication.status !== 'OFFERED') ||
        (requestedStatus === 'ACCEPTED_BY_APPLICANT' && existingApplication.status !== 'OFFERED')
      ) {
        throw new AppError('APPL004');
      }
      application = await prismaPG.jobApplication.update({
        select: { id: true },
        data: {
          status: requestedStatus,
        },
        where: {
          id: applicationId,
        },
      });
      return application;
    } else {
      const matchingResult = await prismaPG.$queryRaw<{ matching: number }[]>`
        SELECT ${JobUtil.Sql.getMatching(selfProfileId)}
      `;
      application = await prismaPG.jobApplication.create({
        select: { id: true },
        data: {
          applicantId: selfProfileId,
          jobId,
          entityId:job.entityId,
          entityRawDataId:job.entityRawDataId,
          matching: NumberUtil.ceilToTwoDecimals(matchingResult?.[0]?.matching || 0),
          status: requestedStatus,
          decisionMakerProfileId:job.profileId
        },
      });
    }
    return application;
  },
  updateOneForEntityMember: async (
    state: FastifyStateI,
    { applicationId, status: requestedStatus }: Application.UpdateOneForEntityMemberI,
  ): Promise<Pick<Application.Application, 'id'>> => {
    const selfProfileId = state.profileId;
    const existingApplication = await prismaPG.jobApplication.findUnique({
      select: { entityId: true, entityRawDataId: true, status: true },
      where: { id: applicationId },
    });
    if (!existingApplication) {
      throw new AppError('APPL001');
    }
    await EntityMemberModule.isAnyMember({
      entity: existingApplication?.entityId
        ? { dataType: 'master', id: existingApplication.entityId }
        : { dataType: 'raw', id: existingApplication.entityRawDataId },
      profileId: selfProfileId,
    });
    if (
      (requestedStatus === 'SHORTLISTED' &&
        !(['PENDING', 'REJECTED_BY_ENTITY'] as ApplicationE.StatusI[]).includes(existingApplication.status)) ||
      (requestedStatus === 'REJECTED_BY_ENTITY' &&
        !(['PENDING', 'SHORTLISTED', 'OFFERED', 'ACCEPTED_BY_APPLICANT'] as ApplicationE.StatusI[]).includes(
          existingApplication.status,
        )) ||
      (requestedStatus === 'OFFERED' &&
        !(['PENDING', 'SHORTLISTED', 'REJECTED_BY_ENTITY'] as ApplicationE.StatusI[]).includes(
          existingApplication.status,
        ))
    ) {
      throw new AppError('APPL004');
    }
    const application = await prismaPG.jobApplication.update({
      select: { id: true },
      data: {
        status: requestedStatus,
      },
      where: {
        id: applicationId,
      },
    });
    return application;
  },
  fetchFiltersForApplicant: async (state: FastifyStateI, { status }: Application.FetchManyForApplicantQueryI) => {
    const selfProfileId = state.profileId;
    const conditions: Prisma.Sql[] = [Prisma.sql`ja."applicantId" = ${selfProfileId}::uuid`];

    if (status) {
      conditions.push(Prisma.sql`ja.status = ${status}`);
    }

    const whereClause = conditions.length > 0 ? Prisma.sql`WHERE ${Prisma.join(conditions, ' AND ')}` : Prisma.empty;

    const [countryResult, designationsResult, internetResults, shipTypeResults] = await Promise.all([
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT j."countryIso2" as id, c.name as label, COUNT(*) as count
        FROM company."Job" j
        LEFT JOIN master."Country" c ON j."countryIso2" = c.iso2
        INNER JOIN company."JobApplication" ja ON j.id = ja."jobId"
        ${whereClause}
        GROUP BY j."countryIso2", c.name
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          COALESCE(da.name, dr.name) as label,
          COALESCE(j."designationAlternativeId"::text, j."designationRawDataId"::text) as id,
          COUNT(*) as count,
          CASE
            WHEN j."designationAlternativeId" IS NOT NULL THEN 'master'
            ELSE 'raw'
          END as "dataType"
        FROM company."Job" j
        LEFT JOIN company."DesignationAlternative" da ON j."designationAlternativeId" = da.id
        LEFT JOIN "rawData"."DesignationRawData" dr ON j."designationRawDataId" = dr.id
        INNER JOIN company."JobApplication" ja ON j.id = ja."jobId"
        ${whereClause}
        GROUP BY label, j."designationAlternativeId", j."designationRawDataId"
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          CASE
            WHEN jb."internetAvailable" = false THEN '0'
            WHEN jb."internetLimitPerDay" < 200 THEN '<200'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200-500'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500-1000'
            WHEN jb."internetLimitPerDay" > 1000 THEN '1000+'
            ELSE 'unknown'
          END as id,
          CASE
            WHEN jb."internetAvailable" = false THEN 'No Internet'
            WHEN jb."internetLimitPerDay" < 200 THEN '< 200MB'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200MB - 500MB'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500MB - 1000MB'
            WHEN jb."internetLimitPerDay" > 1000 THEN '> 1000MB'
            ELSE 'Unknown'
          END as label,
          COUNT(*) as count
        FROM company."Job" j
        LEFT JOIN company."JobBenefit" jb ON j.id = jb."jobId"
        INNER JOIN company."JobApplication" ja ON j.id = ja."jobId"
        ${whereClause}
        GROUP BY
          jb."internetAvailable",
          jb."internetLimitPerDay",
          CASE
            WHEN jb."internetAvailable" = false THEN '0'
            WHEN jb."internetLimitPerDay" < 200 THEN '<200'
            WHEN jb."internetLimitPerDay" BETWEEN 200 AND 500 THEN '200-500'
            WHEN jb."internetLimitPerDay" BETWEEN 501 AND 1000 THEN '500-1000'
            WHEN jb."internetLimitPerDay" > 1000 THEN '1000+'
            ELSE 'unknown'
          END
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          COALESCE(svt.name, svrw.name) as label,
          COALESCE(j."shipTypeId", j."shipTypeRawDataId") as id,
          COUNT(*) as count,
          CASE
            WHEN j."shipTypeId" IS NOT NULL THEN 'master'
            ELSE 'raw'
          END as "dataType"
        FROM company."Job" j
        INNER JOIN company."JobApplication" ja ON j.id = ja."jobId"
        LEFT JOIN ship."SubVesselType" svt
          ON j."shipTypeId" = svt.id
        LEFT JOIN "rawData"."SubVesselTypeRawData" svrw
          ON j."shipTypeRawDataId" = svrw.id
        ${whereClause}
        GROUP BY label, j."shipTypeId", j."shipTypeRawDataId"
      `,
    ]);

    const filters: JobFiltersParamsI = {
      locations: [],
      designations: [],
      shipTypes: [],
      internetLimits: []
    };

    countryResult.forEach((country) => {
      filters.locations.push({
        id: country.id,
        label: country.label,
        count: Number(country.count),
      });
    });

    designationsResult.forEach((designation) => {
      filters.designations.push({
        id: designation.id,
        label: designation.label,
        count: Number(designation.count),
        dataType: designation.dataType,
      });
    });

    internetResults.map((item) => {
      filters.internetLimits.push({
        id:item.id,
        label:item.label,
        count:Number(item.count)
      })
    })

    shipTypeResults.forEach((shipType) => {
      filters.shipTypes.push({
        id: shipType.id,
        label: shipType.label,
        count: Number(shipType.count),
        dataType: 'master',
      });
    });

    return filters;
  },
  fetchFiltersForEntityMember: async (
    state: FastifyStateI,
    { status, jobId }: Application.FetchManyForEntityMemberQueryI,
  ) => {
    // const selfProfileId = state.profileId;
    let sailing: boolean;

    const jobResult = await prismaPG.job.findUnique({
      where: {
        id: jobId,
      },
      select: {
        jobType: true,
      },
    });

    if (jobResult.jobType === 'SAILING') {
      sailing = true;
    } else {
      sailing = false;
    }

    const conditions: Prisma.Sql[] = [];

    conditions.push(Prisma.sql`ja."jobId" = ${jobId}::uuid`);

    if (status) {
      conditions.push(Prisma.sql`ja.status = ${status}::"company"."ApplicationStatus"`);
    }

    //figure out a way
    /*
    conditions.push(Prisma.sql`NOT EXISTS (
      SELECT 1 FROM user."BlockedProfile" bp
      WHERE (bp."blockerId" = ${selfProfileId}::uuid AND bp."blockedId" = p.id)
      OR (bp."blockedId" = ${selfProfileId}::uuid AND bp."blockerId" = p.id)
    )`);
    */

    const whereClause = conditions.length > 0 ? Prisma.sql`WHERE ${Prisma.join(conditions, ' AND ')}` : Prisma.empty;

    const experienceColumn = sailing
      ? Prisma.sql`SUM(e."sailingYears" + e."sailingMonths" / 12.0)`
      : Prisma.sql`SUM(e."years" + e."months" / 12.0)`;

    const [countryResult, designationsResult, yearsOfExperienceResult] = await Promise.all([
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT p."countryIso2" as id, c.name as label, COUNT(*) as count
        FROM company."JobApplication" ja
        INNER JOIN "user"."Profile" p ON ja."applicantId" = p.id
        LEFT JOIN master."Country" c ON p."countryIso2" = c.iso2
        ${whereClause}
        GROUP BY p."countryIso2", c.name
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        SELECT
          COALESCE(da.name, dr.name, p."designationText") as label,
          COALESCE(p."designationAlternativeId"::text, p."designationRawDataId"::text, p."designationText") as id,
          COUNT(*) as count,
          CASE
            WHEN p."designationAlternativeId" IS NOT NULL THEN 'master'
            WHEN p."designationRawDataId" IS NOT NULL THEN 'raw'
            ELSE 'text'
          END as "dataType"
        FROM company."JobApplication" ja
        INNER JOIN "user"."Profile" p ON ja."applicantId" = p.id
        LEFT JOIN company."DesignationAlternative" da ON p."designationAlternativeId" = da.id
        LEFT JOIN "rawData"."DesignationRawData" dr ON p."designationRawDataId" = dr.id
        ${whereClause}
        GROUP BY label, p."designationAlternativeId", p."designationRawDataId", p."designationText"
      `,
      prismaPG.$queryRaw<IdLabelCountDataTypeI[]>`
        WITH experience_summary AS (
          SELECT
            e."profileId",
            ${experienceColumn} AS total_years
          FROM career."Experience" e
          GROUP BY e."profileId"
        ),
        bucketed AS (
          SELECT
            ja.id AS application_id,
            CASE
              WHEN es.total_years < 1 THEN '<1'
              WHEN es.total_years BETWEEN 1 AND 3 THEN '1-3'
              WHEN es.total_years BETWEEN 3 AND 5 THEN '3-5'
              WHEN es.total_years BETWEEN 5 AND 10 THEN '5-10'
              WHEN es.total_years > 10 THEN '10+'
              ELSE 'Unknown'
            END AS id,
            CASE
              WHEN es.total_years < 1 THEN 'Less than 1 year'
              WHEN es.total_years BETWEEN 1 AND 3 THEN '1-3 years'
              WHEN es.total_years BETWEEN 3 AND 5 THEN '3-5 years'
              WHEN es.total_years BETWEEN 5 AND 10 THEN '5-10 years'
              WHEN es.total_years > 10 THEN '10+ years'
              ELSE 'Unknown'
            END AS label
          FROM company."JobApplication" ja
          INNER JOIN "user"."Profile" p ON ja."applicantId" = p.id
          INNER JOIN experience_summary es ON p.id = es."profileId"
          ${whereClause}
        )
        SELECT
          id,
          label,
          COUNT(DISTINCT application_id) AS count
        FROM bucketed
        GROUP BY id, label
        ORDER BY
          CASE id
            WHEN '<1' THEN 1
            WHEN '1-3' THEN 2
            WHEN '3-5' THEN 3
            WHEN '5-10' THEN 4
            WHEN '10+' THEN 5
            ELSE 6
          END
      `,
    ]);

    const filters: ApplicantFilterForEntityMemberParamsI = {
      locations: [],
      designations: [],
      yearsOfExperiences: [],
    };

    countryResult.forEach((country) => {
      filters.locations.push({
        id: country.id,
        label: country.label,
        count: Number(country.count),
      });
    });

    designationsResult.forEach((designation) => {
      filters.designations.push({
        id: designation.id,
        label: designation.label,
        count: Number(designation.count),
        dataType: designation.dataType,
      });
    });

    yearsOfExperienceResult.forEach((experience) => {
      filters.yearsOfExperiences.push({
        id: experience.id,
        label: experience.label,
        count: Number(experience.count),
      });
    });

    return filters;
  },
};
