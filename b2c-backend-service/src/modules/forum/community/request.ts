import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { FastifyStateI } from '@interfaces/common/declaration';
import { CommunityRequestCreateOneResultI } from '@interfaces/forum/request';
import {
  CommunitRequestApprovalQueryI,
  CommunityRequestApprovalBodyI,
  CommunityRequestBodyI,
  CommunityRequestRejectionQueryI,
  CommunityRequestRevocationQueryI,
  CommunityRequestFetchManyI,
} from '@schemas/forum/request';
import { MemberTypeE, CommunityRequestStatusE, Prisma } from '@prisma/postgres';
import { errorHandler } from '@utils/errors/handler';

const CommunityRequestModule = {
  fetchPendingForCommunity: async (
    state: FastifyStateI,
    { communityId, page, pageSize }: CommunityRequestFetchManyI,
  ): Promise<{
    total: number;
    data: Array<{
      status: CommunityRequestStatusE;
      requestedType: MemberTypeE;
      Profile: {
        id: string;
        avatar: string | null;
        name: string;
        designationText: string | null;
        designationAlternativeId: string | null;
        designationRawDataId: string | null;
        entityText: string | null;
        entityId: string | null;
        entityRawDataId: string | null;
      };
    }>;
  }> => {
    const adminProfileId = state.profileId;

    const adminMember = await prismaPG.communityMember.findFirst({
      where: { profileId: adminProfileId, communityId, type: MemberTypeE.ADMIN },
    });
    if (!adminMember) {
      throw new AppError('CMTY004');
    }

    const filters: Prisma.CommunityRequestWhereInput = {
      communityId,
      status: CommunityRequestStatusE.PENDING,
      Profile: { status: 'ACTIVE' },
    } as Prisma.CommunityRequestWhereInput;

    const [total, requests] = await Promise.all([
      prismaPG.communityRequest.count({ where: filters }),
      prismaPG.communityRequest.findMany({
        where: filters,
        orderBy: { updatedAt: 'desc' },
        skip: page,
        take: pageSize,
        select: {
          status: true,
          requestedType: true,
          Profile: {
            select: {
              id: true,
              avatar: true,
              name: true,
              designationText: true,
              designationAlternativeId: true,
              designationRawDataId: true,
              entityText: true,
              entityId: true,
              entityRawDataId: true,
            },
          },
        },
      }),
    ]);

    return { total, data: requests };
  },
  createOne: async ({
    communityId,
    profileId,
    requestedType,
  }: CommunityRequestBodyI): Promise<CommunityRequestCreateOneResultI> => {
    const existingMember = await prismaPG.communityMember.findFirst({
      where: {
        communityId,
        profileId,
      },
    });
    if (existingMember) {
      throw new AppError('CMRQ003');
    }
    const existingRequestAny = await prismaPG.communityRequest.findFirst({
      where: {
        communityId,
        profileId,
      },
    });
    if (existingRequestAny?.status === CommunityRequestStatusE.PENDING) {
      throw new AppError('CMRQ002');
    }
    const community = await prismaPG.community.findUnique({
      where: { id: communityId },
      select: {
        access: true,
        isRestricted: true,
      },
    });

    if (!community) {
      throw new AppError('CMTY001');
    }
    const status: CommunityRequestStatusE = CommunityRequestStatusE.PENDING;
    const acceptedType: MemberTypeE | null = null;
    const result = await prismaPG.$transaction(async (tx) => {
      let communityRequestResult: CommunityRequestCreateOneResultI;

      const existing = await tx.communityRequest.findFirst({
        where: { communityId, profileId, entityProfileId: null } as Prisma.CommunityRequestWhereUniqueInput,
      });

      if (existing) {
        if (existing.status === CommunityRequestStatusE.PENDING) {
          throw new AppError('CMRQ002');
        }
        communityRequestResult = await tx.communityRequest.update({
          where: { communityId, profileId, entityProfileId: null } as Prisma.CommunityRequestWhereUniqueInput,
          data: { requestedType, acceptedType, status },
        });
      } else {
        communityRequestResult = await tx.communityRequest.create({
          data: { communityId, profileId, requestedType, acceptedType, status },
        });
      }
      // if (status === CommunityRequestStatusE.ACCEPTED && acceptedType) {
      //   await tx.communityMember.create({ data: { communityId, profileId, type: acceptedType } });
      //   await tx.community.update({ where: { id: communityId }, data: { memberCount: { increment: 1 } } });
      // }
      return communityRequestResult;
    });

    return result;
  },
  approveRequestTypeChange: async (
    state: FastifyStateI,
    { profileId, communityId }: CommunitRequestApprovalQueryI,
    { acceptedType }: CommunityRequestApprovalBodyI,
  ): Promise<CommunityRequestCreateOneResultI> => {
    const adminProfileId = state.profileId;
    const communityMemberResult = await prismaPG.communityMember.findFirst({
      where: {
        profileId: adminProfileId,
        communityId,
      },
    });

    if (!communityMemberResult || communityMemberResult.type !== 'ADMIN') {
      throw new AppError('CMTY004');
    }

    const request = await prismaPG.communityRequest.findFirst({
      where: {
        profileId,
        communityId,
        status: CommunityRequestStatusE.PENDING,
        entityProfileId:null,
      },
      select:{
        id:true,
        requestedType:true
      }
    });
    if (!request) {
      throw new AppError('CMRQ004');
    }
    const status =
      request.requestedType === acceptedType
        ? CommunityRequestStatusE.ACCEPTED
        : CommunityRequestStatusE.PARTIALLY_ACCEPTED;

    const result = await prismaPG.$transaction(async (tx) => {
      const communityRequestResult = await tx.communityRequest.update({
        where: {
          id: request.id
        },
        data: {
          acceptedType,
          status,
        },
      });

      if ([CommunityRequestStatusE.ACCEPTED, CommunityRequestStatusE.PARTIALLY_ACCEPTED].includes(status)) {
        await prismaPG.$transaction([
          tx.communityMember.create({
            data: { communityId, profileId, type: acceptedType },
          }),
          tx.community.update({
            where: { id: communityId },
            data: { memberCount: { increment: 1 } },
          }),
        ]);
      }
      return communityRequestResult;
    });

    return result;
  },
  rejectRequest: async (
    state: FastifyStateI,
    { profileId, communityId }: CommunityRequestRejectionQueryI,
  ): Promise<void> => {
    try {
      const adminProfileId = state.profileId;
      const adminMember = await prismaPG.communityMember.findFirst({
        where: {
          profileId: adminProfileId,
          communityId,
          type: MemberTypeE.ADMIN,
        },
      });

      if (!adminMember) {
        throw new AppError('CMTY004');
      }

      const request = await prismaPG.communityRequest.findFirst({
        where: {
          profileId,
          communityId,
          status: CommunityRequestStatusE.PENDING,
        },
      });

      if (!request) {
        throw new AppError('CMRQ004');
      }

      await prismaPG.communityRequest.delete({
        where: { communityId, profileId, entityProfileId: null } as Prisma.CommunityRequestWhereUniqueInput,
      });
    } catch (error) {
      errorHandler(error);
    }
  },

  revokeRequest: async ({ profileId, communityId }: CommunityRequestRevocationQueryI): Promise<void> => {
    const request = await prismaPG.communityRequest.findFirst({
      where: {
        profileId,
        communityId,
        status: CommunityRequestStatusE.PENDING,
      },
    });

    if (!request) {
      throw new AppError('CMRQ004');
    }

    await prismaPG.communityRequest.update({
      where: { communityId, profileId, entityProfileId: null } as Prisma.CommunityRequestWhereUniqueInput,
      data: {
        status: CommunityRequestStatusE.REVOKED,
      },
    });
  },
};

export default CommunityRequestModule;
