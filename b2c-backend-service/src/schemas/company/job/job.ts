import AppError from '@classes/AppError';
import type { Job as JobModel } from '@prisma/postgres';
import { UUIDSchema } from '@navicater/b2c-internal-communication';
import { CountryIso2Schema, CursorPaginationSchema, IdLabelSchema, IdTypeOprSchema, IdTypeSchema } from '@schemas/common/common';
import { ShipImoClientSchema } from '@schemas/ship/ship';
import { getCurrentDate, addDaysToDateOnly } from '@utils/data/date';
import z from 'zod';
import { JobE } from '@consts/company/job/job';
import {
  DateNullI,
  DateUndefinedNullI,
  IdNameTypeI,
  NumberNullI,
  NumberUndefinedNullI,
  StringNullI,
  UndefinedNullableI,
} from '@interfaces/common/data';
import { ApplicationE } from '@consts/company/job/application';
import { Application } from './application';
import { ShipNestedClientI } from '@interfaces/ship/ship';
import { SubVesselTypeNestedClientI } from '@interfaces/ship/subVesselType';
import { ProfileExternalI } from '@interfaces/user/profile';

export interface JobCertificationRequirementI {
  id: string;
  isMandatory: boolean;
  CertificateCourse?: { id: string; name: string } | null;
  CertificateCourseRawData?: { id: string; name: string } | null;
}

export interface JobDocumentRequirementI {
  id: string;
  countries: string[];
  isMandatory: boolean;
  description?: string | null;
  sequence: number;
  DocumentType?: { id: string; name: string } | null;
  DocumentTypeRawData?: { id: string; name: string } | null;
}

export interface JobExperienceRequirementI {
  id: string;
  monthsOfExperience: number;
  isMandatory: boolean;
  isTotal: boolean;
  DesignationAlternative?: { id: string; name: string } | null;
  DesignationRawData?: { id: string; name: string } | null;
  SubVesselType?: { id: string; name: string } | null;
  SubVesselTypeRawData?: { id: string; name: string } | null;
}

export interface JobSkillRequirementI {
  id: string;
  isMandatory: boolean;
  Skill?: { id: string; name: string } | null;
  SkillRawData?: { id: string; name: string } | null;
}

export interface JobCargoRequirementI {
  id: string;
  name: string;
  code?: string | null;
  monthsOfExperience: number;
  isMandatory: boolean;
}

export interface JobOtherRequirementI {
  id: string;
  sequenceNumber: number;
  details: string;
  isMandatory: boolean;
}

export interface JobEquipmentRequirementI {
  id: string;
  monthsOfExperience: number;
  isMandatory: boolean;
  EquipmentCategory?: { id: string; name: string } | null;
  EquipmentCategoryRawData?: { id: string; name: string } | null;
  EquipmentManufacturer?: { id: string; name: string } | null;
  EquipmentManufacturerRawData?: { id: string; name: string } | null;
  EquipmentModel?: { id: string; name: string } | null;
  EquipmentModelRawData?: { id: string; name: string } | null;
  FuelType?: { id: string; name: string } | null;
  FuelTypeRawData?: { id: string; name: string } | null;
}

export interface JobBenefitDetailI {
  id: string;
  sequenceNumber: number;
  details: string;
}

export namespace Job {
  export type Job = JobModel;

  export const CreateOneSchema = z
    .object({
      entity: IdTypeSchema,
      designation: IdTypeSchema,
      department: IdTypeSchema,
      expiryDate: z.coerce.date(),
      ship: ShipImoClientSchema.optional(),
      isOfficial: z.boolean(),
      isUrgent: z.boolean().optional(),
      minYears: z.number().int().min(0).max(100),
      maxYears: z.number().int().min(0).max(100).optional(),
      minSalary: z.number().int().min(0),
      maxSalary: z.number().int().min(0).optional(),
      entityBenefits: z.array(IdTypeSchema).min(1).max(10).optional(),
    })
    .superRefine((data, _ctx) => {
      const currentDate = getCurrentDate();
      if (currentDate > data.expiryDate) {
        throw new AppError('JOB006');
      }
    });

  export type CreateOneI = z.infer<typeof CreateOneSchema>;

  export const ApplySchema = z.object({
    jobId: UUIDSchema,
    ship: ShipImoClientSchema.optional(),
    isUrgent: z.boolean().optional(),
    minYears: z.number().int().min(0).max(100).optional(),
    maxYears: z.number().int().min(0).max(100).optional(),
    entityBenefits: z.array(IdTypeOprSchema).min(1).max(10).optional(),
    equipmentManufacturers: z.array(IdTypeOprSchema).min(1).max(10).optional(),
    equipmentModels: z.array(IdTypeOprSchema).min(1).max(10).optional(),
    equipmentCategories: z.array(IdTypeOprSchema).min(1).max(10).optional(),
    expiryDate: z.coerce.date().optional(),
  });

  export const FetchManyForEntityMemberQuerySchema = CursorPaginationSchema.extend({
    isOfficial: z
      .string()
      .refine((val) => val === 'true' || val === 'false', {
        message: "isOfficial must be 'true' or 'false'",
      })
      .transform((val) => val === 'true'),
    status: JobE.Status.optional(),
  });
  export type FetchManyForEntityMemberQueryI = z.infer<typeof FetchManyForEntityMemberQuerySchema>;

  export const FetchManyForEntityMemberBodySchema = z.object({
    entity: IdTypeSchema.optional().nullable(),
    designations: z.array(IdTypeSchema).optional(),
    shipTypes: z.array(IdTypeSchema).optional(),
  });
  export type FetchManyForEntityMemberBodyI = z.infer<typeof FetchManyForEntityMemberBodySchema>;

  export const FetchManyForCandidateQuerySchema = CursorPaginationSchema.extend({
    entity: IdTypeSchema.optional(),
    isOfficial: z.coerce.boolean(),
    status: JobE.Status.optional(),
  });
  export type FetchManyForCandidateQueryI = z.infer<typeof FetchManyForCandidateQuerySchema>;
  export const FetchManyForCandidateBodySchema = z.object({
    designations: z.array(IdTypeSchema).optional().nullable(),
    countries: z.array(CountryIso2Schema).optional().nullable(),
    shipTypes: z.array(IdTypeSchema).optional().nullable(),
    internetLimits: z.array(IdLabelSchema).optional().nullable()
  });
  export type FetchManyForCandidateBodyI = z.infer<typeof FetchManyForCandidateBodySchema>;

  export const FetchFiltersForCandidateSchema = z.object({
    entity: IdTypeSchema.optional(),
    isOfficial: z.boolean().optional(),
    status: JobE.Status.optional(),
  });
  export type FetchFiltersForCandidateI = z.infer<typeof FetchFiltersForCandidateSchema>;

  export type FetchManyForEntityMemberResultI = Pick<
    Job,
    | 'id'
    | 'cursorId'
    | 'isOfficial'
    | 'status'
    | 'createdAt'
    | 'isUrgent'
    | 'expiryDate'
    | 'maxSalary'
    | 'maxYears'
    | 'minSalary'
    | 'minYears'
  > &
    Partial<Pick<Application.Application, 'matching'>> & {
      applicationStatus?: ApplicationE.StatusI;
      designation: IdNameTypeI;
      entity: IdNameTypeI;
    };
  export const SearchForCandidateSchema = CursorPaginationSchema.extend({
    jobId: UUIDSchema.optional(),
    entity: IdTypeSchema.optional(),
    isOfficial: z.boolean().optional(),
    status: JobE.Status.optional(),
    applicationStatus: ApplicationE.Status.optional(),
    designations: z.array(IdTypeSchema).optional(),
    countries: z.array(CountryIso2Schema).optional(),
    shipTypes: z.array(IdTypeSchema).optional(),
    internetLimits: z.array(IdLabelSchema).optional()
  }).superRefine((data, _ctx) => {
    if (typeof data.isOfficial !== 'boolean') {
      if (!data?.jobId) {
        throw new AppError('JOB013');
      }
    }
  });
  export type SearchForCandidateI = z.infer<typeof SearchForCandidateSchema>;

  export type SearchForCandidateSQLI = {
    jobId: string;
    cursorId: string;
    isUrgent: boolean;
    entityId: StringNullI;
    entityName: StringNullI;
    entityRawDataId: StringNullI;
    entityRawDataName: StringNullI;

    designationAlternativeId: StringNullI;
    designationName: StringNullI;
    designationRawDataId: StringNullI;
    designationRawDataName: StringNullI;

    departmentAlternativeId: StringNullI;
    departmentName: StringNullI;
    departmentRawDataId: StringNullI;
    departmentRawDataName: StringNullI;

    shipImo: StringNullI;
    shipName: StringNullI;
    shipRawDataImo: StringNullI;
    shipRawDataName: StringNullI;
    shipTypeId: StringNullI;
    shipTypeName: StringNullI;
    shipTypeRawDataId: StringNullI;
    shipTypeRawDataName: StringNullI;
    showShipDetails: boolean;
    applicationMethod: string;
    applicationEmail: StringNullI;
    applicationUrl: StringNullI;
    minYears: NumberNullI;
    maxYears: NumberNullI;
    minSalary: NumberNullI;
    maxSalary: NumberNullI;
    showSalary: boolean;
    status: string;
    expiryDate: DateNullI;
    isOfficial: boolean;
    createdAt: Date;
    applicationStatus: StringNullI;

    creatorId: string;
    creatorName: string;
    creatorAvatar: StringNullI;
    creatorDesignationText: StringNullI;
    creatorDesignationAlternativeId: StringNullI;
    creatorDesignationRawDataId: StringNullI;
    creatorEntityText: StringNullI;
    creatorEntityId: StringNullI;
    creatorEntityRawDataId: StringNullI;

    matching: number;

    entityProfileId: StringNullI;
    entityProfileName: StringNullI;
    entityProfileAvatar: StringNullI;
  };

  export type FetchOneForCandidateSQLI = {
    jobId: string;
    cursorId: string;
    isUrgent: boolean;
    entityId: StringNullI;
    entityName: StringNullI;
    entityRawDataId: StringNullI;
    entityRawDataName: StringNullI;

    designationAlternativeId: StringNullI;
    designationName: StringNullI;
    designationRawDataId: StringNullI;
    designationRawDataName: StringNullI;

    departmentAlternativeId: StringNullI;
    departmentName: StringNullI;
    departmentRawDataId: StringNullI;
    departmentRawDataName: StringNullI;

    shipImo: StringNullI;
    shipName: StringNullI;
    shipRawDataImo: StringNullI;
    shipRawDataName: StringNullI;
    shipTypeId: StringNullI;
    shipTypeName: StringNullI;
    shipTypeRawDataId: StringNullI;
    shipTypeRawDataName: StringNullI;
    showShipDetails: boolean;
    applicationMethod: string;
    applicationEmail: StringNullI;
    applicationUrl: StringNullI;
    minYears: NumberNullI;
    maxYears: NumberNullI;
    minSalary: NumberNullI;
    maxSalary: NumberNullI;
    status: string;
    expiryDate: DateNullI;
    isOfficial: boolean;
    createdAt: Date;
    applicationStatus: StringNullI;

    // Additional job details
    about: StringNullI;
    rolesResponsibilities: StringNullI;
    basicRequirements: StringNullI;
    basicBenefits: StringNullI;
    salaryType: StringNullI;
    currencyCode: StringNullI;
    showSalary: boolean;
    jobType: StringNullI;
    countryIso2: StringNullI;
    joiningDate: DateNullI;
    genderDiversityIndex: NumberNullI;
    requirementType: StringNullI;
    benefitType: StringNullI;

    // Advanced benefit details
    contractMonths: NumberNullI;
    contractDays: NumberNullI;
    internetAvailable: boolean;
    internetSpeed: NumberNullI;
    internetLimitPerDay: NumberNullI;
    internetDetails: StringNullI;
    insuranceType: StringNullI;
    familyOnboard: boolean;
    itfType: StringNullI;

    // Country details
    countryName: StringNullI;

    creatorId: string;
    creatorName: string;
    creatorAvatar: StringNullI;
    creatorDesignationText: StringNullI;
    creatorDesignationAlternativeId: StringNullI;
    creatorDesignationRawDataId: StringNullI;
    creatorEntityText: StringNullI;
    creatorEntityId: StringNullI;
    creatorEntityRawDataId: StringNullI;

    matching: number;
  };

  export type SearchForCandidateResultI = {
    id: string;
    cursorId: string;
    isUrgent: boolean;
    entity: IdNameTypeI;
    designation: IdNameTypeI;
    department: IdNameTypeI;
    ship: ShipNestedClientI | null;
    shipType: SubVesselTypeNestedClientI | null;
    showShipDetails: boolean;
    applicationMethod: 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK';
    applicationEmail?: string;
    applicationUrl?: string;

    minYears: number;
    maxYears: NumberUndefinedNullI;
    minSalary: number;
    maxSalary: NumberUndefinedNullI;
    showSalary: boolean;
    status?: UndefinedNullableI<JobE.StatusI>;
    expiryDate: DateUndefinedNullI;
    isOfficial: boolean;
    createdAt: Date;
    applicationStatus: UndefinedNullableI<ApplicationE.StatusI>;
    creator: ProfileExternalI;

    matching?: UndefinedNullableI<number>;
  };

  export type FetchOneForCandidateResultI = {
    id: string;
    cursorId: string;
    isUrgent: boolean;
    entity: IdNameTypeI;
    designation: IdNameTypeI;
    department: IdNameTypeI;
    ship: ShipNestedClientI | null;
    shipType: SubVesselTypeNestedClientI | null;
    applicationMethod: 'IN_APP' | 'EMAIL' | 'EXTERNAL_LINK';
    applicationEmail?: string;
    applicationUrl?: string;

    minYears: number;
    maxYears: NumberUndefinedNullI;
    minSalary: number;
    maxSalary: NumberUndefinedNullI;
    status?: UndefinedNullableI<JobE.StatusI>;
    expiryDate: DateUndefinedNullI;
    isOfficial: boolean;
    createdAt: Date;
    applicationStatus: UndefinedNullableI<ApplicationE.StatusI>;
    creator: ProfileExternalI;

    // Job details
    about?: StringNullI;
    rolesResponsibilities?: StringNullI;
    requirements?: StringNullI;
    benefits?: StringNullI;
    salaryType?: StringNullI;
    currencyCode?: StringNullI;
    showSalary?: boolean;
    jobType?: StringNullI;
    countryIso2?: StringNullI;
    joiningDate?: DateNullI;
    genderDiversityIndex?: NumberNullI;
    countryName?: StringNullI;
    requirementType?: StringNullI;
    benefitType?: StringNullI;
    showShipDetails?: boolean;

    // Advanced benefit fields
    contractMonths?: NumberNullI;
    contractDays?: NumberNullI;
    internetAvailable?: boolean;
    internetSpeed?: NumberNullI;
    internetLimitPerDay?: NumberNullI;
    internetDetails?: StringNullI;
    insuranceType?: StringNullI;
    familyOnboard?: boolean;
    itfType?: StringNullI;

    // Advanced requirements fields
    certificationRequirements?: JobCertificationRequirementI[];
    documentRequirements?: JobDocumentRequirementI[];
    experienceRequirements?: JobExperienceRequirementI[];
    skillRequirements?: JobSkillRequirementI[];
    cargoRequirements?: JobCargoRequirementI[];
    otherRequirements?: JobOtherRequirementI[];
    equipmentRequirements?: JobEquipmentRequirementI[];

    // Advanced benefit details
    benefitDetails?: JobBenefitDetailI[];

    matching?: UndefinedNullableI<number>;
  };

  export const CreateJobDetailsSchema = z
    .object({
      entity: IdTypeSchema,
      designation: IdTypeSchema,
      ship: ShipImoClientSchema.optional(),
      shipType: IdTypeSchema.optional(),
      showShipDetails: z.boolean().default(true),
      applicationMethod: z.enum(['IN_APP', 'EMAIL', 'EXTERNAL_LINK']).default('IN_APP'),
      applicationEmail: z.string().email().max(255).optional(),
      applicationUrl: z.string().url().max(2000).optional(),
      jobType: z.enum(['SAILING', 'NON_SAILING']),
      countryIso2: CountryIso2Schema.optional().nullable(),
      expiryDate: z.coerce.date(),
      joiningDate: z.coerce.date().optional(),
      genderDiversityIndex: z.number().min(0).max(1),
      isOfficial: z.boolean(),
      isUrgent: z.boolean().optional(),
      minYears: z.number().int().min(0).max(100).optional(),
      maxYears: z.number().int().min(0).max(100).optional(),
    })
    .superRefine((data, ctx) => {
      const currentDate = getCurrentDate();

      if (currentDate > data.expiryDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Expiry date cannot be in the past',
          path: ['expiryDate'],
        });
      }

      const maxExpiryDate = addDaysToDateOnly({ date: currentDate, days: 60 });
      if (data.expiryDate > maxExpiryDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Expiry date cannot be more than 2 months from today',
          path: ['expiryDate'],
        });
      }

      if (data.joiningDate && data.joiningDate < data.expiryDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Joining date must be on or after expiry date',
          path: ['joiningDate'],
        });
      }

      if (data.minYears && data.maxYears && data.minYears > data.maxYears) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Maximum years must be greater than minimum years',
          path: ['maxYears'],
        });
      }

      if (data.applicationMethod === 'EMAIL') {
        if (!data.applicationEmail) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email is required when application method is EMAIL",
            path: ["applicationEmail"]
          });
        }
        if (data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "URL should not be provided when application method is EMAIL",
            path: ["applicationUrl"]
          });
        }
      }

      if (data.applicationMethod === 'EXTERNAL_LINK') {
        if (!data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "URL is required when application method is EXTERNAL_LINK",
            path: ["applicationUrl"]
          });
        }
        if (data.applicationEmail) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email should not be provided when application method is EXTERNAL_LINK",
            path: ["applicationEmail"]
          });
        }
      }

      if (data.applicationMethod === 'IN_APP') {
        if (data.applicationEmail || data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email and URL should not be provided when application method is IN_APP",
            path: ["applicationMethod"]
          });
        }
      }
    });

  export type CreateJobDetailsI = z.infer<typeof CreateJobDetailsSchema>;

  export const UpdateJobDetailsSchema = z
    .object({
      jobId: UUIDSchema,
      entity: IdTypeSchema.optional(),
      designation: IdTypeSchema.optional(),
      ship: ShipImoClientSchema.optional(),
      shipType: IdTypeSchema.optional(),
      showShipDetails: z.boolean().optional(),
      applicationMethod: z.enum(['IN_APP', 'EMAIL', 'EXTERNAL_LINK']).optional(),
      applicationEmail: z.string().email().max(255).optional(),
      applicationUrl: z.string().url().max(2000).optional(),
      jobType: z.enum(['SAILING', 'NON_SAILING']).optional(),
      countryIso2: CountryIso2Schema.optional(),
      expiryDate: z.coerce.date().optional(),
      joiningDate: z.coerce.date().optional(),
      genderDiversityIndex: z.number().min(0).max(1).optional(),
      isOfficial: z.boolean().optional(),
      isUrgent: z.boolean().optional(),
      minYears: z.number().int().min(0).max(100).optional(),
      maxYears: z.number().int().min(0).max(100).optional(),
    })
    .superRefine((data, ctx) => {
      if (data.expiryDate) {
        const currentDate = getCurrentDate();

        if (currentDate > data.expiryDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Expiry date cannot be in the past',
            path: ['expiryDate'],
          });
        }

        const maxExpiryDate = addDaysToDateOnly({ date: currentDate, days: 60 });
        if (data.expiryDate > maxExpiryDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Expiry date cannot be more than 2 months from today',
            path: ['expiryDate'],
          });
        }

        if (data.joiningDate && data.joiningDate < data.expiryDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Joining date must be on or after expiry date',
            path: ['joiningDate'],
          });
        }
      }

      if (data.minYears && data.maxYears && data.minYears > data.maxYears) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Maximum years must be greater than minimum years',
          path: ['maxYears'],
        });
      }

      if (data.applicationMethod === 'EMAIL') {
        if (!data.applicationEmail) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email is required when application method is EMAIL",
            path: ["applicationEmail"]
          });
        }
        if (data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "URL should not be provided when application method is EMAIL",
            path: ["applicationUrl"]
          });
        }
      }

      if (data.applicationMethod === 'EXTERNAL_LINK') {
        if (!data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "URL is required when application method is EXTERNAL_LINK",
            path: ["applicationUrl"]
          });
        }
        if (data.applicationEmail) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email should not be provided when application method is EXTERNAL_LINK",
            path: ["applicationEmail"]
          });
        }
      }

      if (data.applicationMethod === 'IN_APP') {
        if (data.applicationEmail || data.applicationUrl) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Email and URL should not be provided when application method is IN_APP",
            path: ["applicationMethod"]
          });
        }
      }
    });

  export type UpdateJobDetailsI = z.infer<typeof UpdateJobDetailsSchema>;

  export const UpdateJobRequirementsSchema = z.object({
    jobId: UUIDSchema,
    about: z.string().max(5000).optional(),
    rolesResponsibilities: z.string().max(5000).optional(),
    requirementType: z.enum(['BASIC', 'ADVANCED']).default('BASIC'),
    basicRequirements: z.string().max(5000).optional(),

    certificationRequirements: z.array(z.object({
      certification: IdTypeSchema,
      isMandatory: z.boolean().default(false)
    })).optional(),

    documentRequirements: z.array(z.object({
      documentType: IdTypeSchema,
      countries: z.array(CountryIso2Schema).optional(),
      isMandatory: z.boolean().default(false),
      description: z.string().max(500).optional()
    })).optional(),

    experienceRequirements: z.array(z.object({
      designation: IdTypeSchema.optional(),
      shipType: IdTypeSchema.optional(),
      monthsOfExperience: z.number().int().min(0).max(600),
      isMandatory: z.boolean().default(false),
      isTotal: z.boolean().default(false)
    })).superRefine((items, ctx) => {
      items.forEach((item, index) => {
        if (!item.isTotal && !item.designation) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Designation is required when isTotal is false",
            path: [index, "designation"]
          });
        }
      });
    }).optional(),

    skillRequirements: z.array(z.object({
      skill: IdTypeSchema,
      isMandatory: z.boolean().default(false)
    })).optional(),

    cargoRequirements: z.array(z.object({
      name: z.string().max(100),
      code: z.string().max(50).optional(),
      monthsOfExperience: z.number().int().min(0).max(600),
      isMandatory: z.boolean().default(false)
    })).optional(),

    otherRequirements: z.array(z.object({
      details: z.string().max(1000),
      isMandatory: z.boolean().default(false)
    })).optional(),

    equipmentRequirements: z.array(z.object({
      equipmentCategory: IdTypeSchema.optional(),
      equipmentManufacturer: IdTypeSchema.optional(),
      equipmentModel: IdTypeSchema.optional(),
      fuelType: IdTypeSchema.optional(),
      monthsOfExperience: z.number().int().min(0).max(600),
      isMandatory: z.boolean().default(false)
    })).optional(),
  });

  export type UpdateJobRequirementsI = z.infer<typeof UpdateJobRequirementsSchema>;

  export const UpdateJobRequirementsMobileSchema = z.object({
    jobId: UUIDSchema,
    about: z.string().max(5000).optional(),
    rolesResponsibilities: z.string().max(5000).optional(),
    requirements: z.string().max(5000).optional(),
    benefits: z.string().max(2000).optional(),
  });

  export type UpdateJobRequirementsMobileI = z.infer<typeof UpdateJobRequirementsMobileSchema>;

  export const UpdateJobBenefitsSchema = z
    .object({
      jobId: UUIDSchema,
      benefitType: z.enum(['BASIC', 'ADVANCED']).default('BASIC'),
      basicBenefits: z.string().max(2000).optional(),
      salaryType: z.enum(['CONTRACTUAL', 'ROUND_THE_YEAR', 'OTHER']).optional(),
      currencyCode: z.string().length(3).optional(),
      minSalary: z.number().int().min(0).optional(),
      maxSalary: z.number().int().min(0).optional(),
      showSalary: z.boolean().default(true),
      contractMonths: z.number().int().min(1).optional(),
      contractDays: z.number().int().min(1).optional(),
      internetAvailable: z.boolean().optional(),
      internetSpeed: z.number().int().min(0).optional(),
      internetLimitPerDay: z.number().int().min(0).optional(),
      internetDetails: z.string().max(500).optional(),
      insuranceType: z.enum(['SELF', 'FAMILY', 'OTHER']).optional(),
      familyOnboard: z.boolean().optional(),
      itfType: z.enum(['ITF', 'NON_ITF']).optional(),
      benefitDetails: z
        .array(
          z.object({
            details: z.string().max(1000),
          }),
        )
        .optional(),
      entityBenefits: z.array(IdTypeSchema).optional(),
    })
    .superRefine((data, ctx) => {
      if (data.minSalary && data.maxSalary && data.minSalary > data.maxSalary) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Maximum salary must be greater than minimum salary',
          path: ['maxSalary'],
        });
      }
    });

  export type UpdateJobBenefitsI = z.infer<typeof UpdateJobBenefitsSchema>;

  const StageParamsSchema = z.object({
    stage: z.union([z.literal('2'), z.literal('3')]),
  });

  export const UpdateJobRequirementsWithParamsSchema = z.intersection(
    UpdateJobRequirementsSchema,
    StageParamsSchema.extend({ stage: z.literal('2') }),
  );
  export type UpdateJobRequirementsWithParamsI = z.infer<typeof UpdateJobRequirementsWithParamsSchema>;

  export const UpdateJobBenefitsWithParamsSchema = z.intersection(
    UpdateJobBenefitsSchema,
    StageParamsSchema.extend({ stage: z.literal('3') }),
  );
  export type UpdateJobBenefitsWithParamsI = z.infer<typeof UpdateJobBenefitsWithParamsSchema>;
}
