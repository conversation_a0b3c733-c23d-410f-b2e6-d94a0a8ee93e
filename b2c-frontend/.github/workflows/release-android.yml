name: 📦 Android Build & Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: '📦 App version (semantic version)'
        required: true
        default: ''
      build-number:
        description: '🔢 Build number'
        required: true
        default: ''
      track:
        description: 'Release track (internal, alpha, beta, production)'
        required: false
        default: ''

concurrency:
  group: android-build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  prepare:
    name: 🛠️ Prepare Environment
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      version: ${{ steps.setup.outputs.version }}
      build-number: ${{ steps.setup.outputs.build-number }}

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ⚙️ Setup Environment
        id: setup
        uses: ./.github/actions/setup-environment
        with:
          platform: 'android'

      - name: 🧩 Set Build Matrix
        id: set-matrix
        run: |
          # Create matrix configuration
          MATRIX_JSON=$(cat <<EOF
          {
            "include": [
              { "branch": "android-prod-release", "track": "production" },
              { "branch": "android-alpha-release", "track": "alpha" },
              { "branch": "android-beta-release", "track": "beta" }
            ]
          }
          EOF
          )

          # Filter based on current branch
          FILTERED=$(echo "$MATRIX_JSON" | jq -c --arg branch "${{ github.ref_name }}" '.include | map(select(.branch == $branch))')

          # Create final matrix
          echo "matrix={\"include\":$FILTERED}" >> $GITHUB_OUTPUT
          echo "Generated matrix: $(cat $GITHUB_OUTPUT | grep matrix)"

  build:
    name: 🏗️ Build & Release
    runs-on: ubuntu-latest
    needs: prepare
    env:
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
      SENTRY_LOG_LEVEL: debug
      CI_SKIP_SENTRY: "false"
      SENTRY_PROPERTIES: android/sentry.properties 
    strategy:
      matrix: ${{ fromJSON(needs.prepare.outputs.matrix) }}
      fail-fast: false

    steps:
      - name: 🔍 Debug Received Outputs
        run: |
          echo "All outputs from prepare job:"
          echo "matrix: ${{ needs.prepare.outputs.matrix }}"
          echo "version: ${{ needs.prepare.outputs.version }}"
          echo "build-number: ${{ needs.prepare.outputs.build-number }}"
          echo "base-url: ${{ needs.prepare.outputs.base-url }}"
          echo "Current branch: ${{ github.ref_name }}"
          echo "Matrix track: ${{ matrix.track }}"
          echo "BASE_URL: ${{ env.BASE_URL }}"

      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check Sentry config (redacted)
        if: env.CI_SKIP_SENTRY != 'true'
        run: |
          echo "SENTRY_PROPERTIES=${SENTRY_PROPERTIES:-android/sentry.properties}"
          test -f "${SENTRY_PROPERTIES:-android/sentry.properties}" || { echo "sentry.properties not found"; exit 1; }
          sed -E 's/(auth\.token=).*/\1***REDACTED***/' "${SENTRY_PROPERTIES:-android/sentry.properties}" | sed -n 'l'
          grep -q "^auth\.token=" "${SENTRY_PROPERTIES:-android/sentry.properties}" || { echo "auth.token missing in sentry.properties"; exit 1; }

      - name: 🛤️ Determine Release Track
        id: determine-track
        run: |
          TRACK="${{ github.event.inputs.track || matrix.track }}"
          echo "Using track: $TRACK"
          echo "track=$TRACK" >> $GITHUB_OUTPUT

      - name: 🏗️ Build Android AAB
        uses: ./.github/actions/build-android
        with:
          version: ${{ github.event.inputs.version }}
          build-number: ${{ github.event.inputs.build-number }}
          keystore-password: ${{ secrets.KEYSTORE_PASSWORD }}
          keystore-alias: ${{ secrets.KEYSTORE_ALIAS }}
          key-password: ${{ secrets.KEY_PASSWORD }}
          keystore-base64: ${{ secrets.ANDROID_KEYSTORE_BASE64 }}
        env:
          API_KEY: ${{ secrets.API_KEY }}
          BASE_URL: ${{ github.ref_name == 'android-prod-release' && secrets.BASE_URL || secrets.BASE_URL_DEV }}
          MAPBOX_DOWNLOADS_TOKEN: ${{ secrets.MAPBOX_DOWNLOADS_TOKEN }}
          AI_URL: ${{ secrets.AI_URL }}
          FIREBASE_ANDROID_APP_ID: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          FIREBASE_IOS_APP_ID: ${{ secrets.FIREBASE_IOS_APP_ID }}
          FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          IOS_CLIENT_ID: ${{ secrets.IOS_CLIENT_ID }}
          SENTRY_DSN_URL: ${{ secrets.SENTRY_DSN_URL }}
          WEB_CLIENT_ID: ${{ secrets.WEB_CLIENT_ID }}
          ENV: ${{ secrets.ENV }}
          MAPBOX_ACCESS_TOKEN: ${{ secrets.MAPBOX_ACCESS_TOKEN }}

      - name: 📊 Display Build Info
        run: |
          echo "✅ Build completed:"
          echo "📦 Version: ${{ github.event.inputs.version }}"
          echo "🔢 Build Number: ${{ github.event.inputs.build-number }}"
          echo "🛤️ Track: ${{ steps.determine-track.outputs.track }}"
          ls -la android/app/build/outputs/bundle/release/

      - name: 📤 Upload to Google Play Console
        uses: ./.github/actions/upload-play-console
        with:
          package-name: com.navicater
          track: ${{ steps.determine-track.outputs.track }}
          service-account-json: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT }}

      - name: 📝 Create GitHub Release
        uses: ncipollo/release-action@v1
        with:
          allowUpdates: true
          tag: android-v${{ github.event.inputs.version }}-${{ github.event.inputs.build-number }}-${{ steps.determine-track.outputs.track }}
          name: 📱 Android Build v${{ github.event.inputs.version }} (${{ steps.determine-track.outputs.track }})
          artifacts: |
            android/app/build/outputs/bundle/release/*.aab
          token: ${{ secrets.PACKAGE_PUBLISH_GITHUB_TOKEN }}
          generateReleaseNotes: true
