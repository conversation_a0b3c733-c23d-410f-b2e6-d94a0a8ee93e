import type React from 'react';
import { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, Pressable, FlatList, RefreshControl } from 'react-native';
import Modal from 'react-native-modal';
import LottieView from 'lottie-react-native';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectCurrentEntityProfile,
  selectEntityProfilesList,
} from '@/src/redux/selectors/entityProfile';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { clearNearbyFilters } from '@/src/redux/slices/announcement/announcementSlice';
import { resetContentState, fetchPosts } from '@/src/redux/slices/content/contentSlice';
import { refreshEntityProfiles } from '@/src/redux/slices/entityprofile/entityProfileListSlice';
import {
  resetEntityProfileState,
  setEntityProfileBasicData,
} from '@/src/redux/slices/entityprofile/entityProfileSlice';
import { setUserActive, setUserInactive } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import Close from '@/src/assets/svgs/Close';
import { updateProfileTypeAPI } from '@/src/networks/source';
import UserAvatar from '../UserAvatar';
import ShimmerBox from '../Shimmer';
import type { SwitchProfileModalPropsI, SwitchProfileTypeI } from './types';

const SwitchProfileModal: React.FC<SwitchProfileModalPropsI> = ({
  visible,
  onClose,
  profiles,
  modalNavigation,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const currentEntity = useSelector(selectCurrentEntityProfile);
  const { items, loading } = useSelector(selectEntityProfilesList);
  const [shouldShowModal, setShouldShowModal] = useState(false);
  const deferredActionRef = useRef<(() => void) | null>(null);
  const [showSwitch, setShowSwitch] = useState(false);
  const [showErrorToast, setShowErrorToast] = useState(false);

  useEffect(() => {
    setShouldShowModal(visible);
  }, [visible]);

  const selectedId = currentUser.isActive ? currentUser.profileId : currentEntity.entityProfileId;

  useEffect(() => {
    if (!showSwitch && showErrorToast) {
      showToast({ type: 'error', message: 'Could not Switch Profiles. Try Again later.' });
      setShowErrorToast(false);
    }
  }, [showSwitch, showErrorToast]);

  const handleUserSelection = async () => {
    try {
      await updateProfileTypeAPI({ type: 'USER' });
      dispatch(setUserActive());
      dispatch(resetEntityProfileState());
      dispatch(resetContentState());
      dispatch(clearNearbyFilters());
      setTimeout(() => {
        dispatch(fetchPosts({ refresh: true }));
      }, 100);
    } catch (e) {
      await updateProfileTypeAPI({ type: 'ENTITY' });
      setShowErrorToast(true);
      throw e;
    }
  };

  const handleEntitySelection = async (entityProfile: SwitchProfileTypeI) => {
    try {
      await updateProfileTypeAPI({ type: 'ENTITY' });

      const entityData = {
        id: entityProfile.id,
        name: entityProfile.name,
        avatar: entityProfile.avatar,
        role: entityProfile.role,
        isVerified: entityProfile.isVerified,
        entity: {
          id: '',
          dataType: 'master'
        }
      };

      dispatch(setEntityProfileBasicData(entityData));
      dispatch(setUserInactive());
      dispatch(resetContentState());
      dispatch(clearNearbyFilters());
      setTimeout(() => {
        dispatch(fetchPosts({ refresh: true }));
      }, 100);
    } catch (e) {
      await updateProfileTypeAPI({ type: 'USER' });
      setShowErrorToast(true);
      throw e;
    }
  };

  const combinedProfiles = useMemo<SwitchProfileTypeI[]>(() => {
    const userProfile: SwitchProfileTypeI = {
      id: currentUser.profileId,
      name: currentUser.fullName,
      avatar: currentUser.avatar,
      type: 'USER',
      role: undefined,
      isVerified: undefined,
    };
    const mapped = items.map((p) => ({
      id: p.id,
      name: p.name,
      avatar: p.avatar,
      type: 'ENTITY' as const,
      role: p.role,
      isVerified: p.isVerified,
      entity:{
        id:p.entity.id,
        dataType:p.entity.dataType
      }
    }));
    if (profiles && profiles.length) return profiles;
    return [userProfile, ...mapped];
  }, [profiles, items, currentUser]);

  // const onRefresh = async () => {
  //   try {
  //     await dispatch(refreshEntityProfiles()).unwrap();
  //   } catch {
  //     showToast({ type: 'error', message: 'Unable to refresh profiles' });
  //   }
  // };

  const close = () => {
    setShouldShowModal(false);
    if (onClose) {
      onClose();
    }
  };

  const handleSelection = (profile: SwitchProfileTypeI) => {
    deferredActionRef.current = async () => {
      try {
        setShowSwitch(true);
        if (profile.type === 'USER') {
          await handleUserSelection();
        } else {
          await handleEntitySelection(profile);
        }
      } finally {
        setTimeout(() => {
          setShowSwitch(false);
          if (onClose) {
            onClose();
          }
        }, 2300);
      }
    };
    setShouldShowModal(false);
  };

  const handleModalHide = () => {
    if (deferredActionRef.current) {
      const fn = deferredActionRef.current;
      deferredActionRef.current = null;
      fn();
    }
  };

  if (showSwitch) {
    return (
      <Modal isVisible animationIn="fadeIn" animationOut="fadeOut" backdropOpacity={0.5}>
        <View className="justify-center items-center bg-white rounded-2xl p-6">
          <LottieView
            source={require('@/src/assets/animations/profileSwitch.json')}
            autoPlay
            loop
            style={{ height: 100, width: 100 }}
          />
        </View>
      </Modal>
    );
  }

  const renderItem = ({ item }: { item: SwitchProfileTypeI }) => {
    const selected = item.id === selectedId;
    return (
      <Pressable
        onPress={() => handleSelection(item)}
        className={selected ? 'rounded-2xl bg-[#d7f3c6]' : ''}
      >
        <View className="flex-row items-center justify-between px-4 py-4">
          <View className="flex-row items-center gap-4">
            <UserAvatar avatarUri={item.avatar} name={item.name} />
            <View className="max-w-[78%]">
              <Text
                className="text-black text-base font-medium"
                style={{ lineHeight: 16, letterSpacing: 0 }}
              >
                {item.name}
              </Text>
            </View>
          </View>
          {selected ? (
            <View className="w-3 h-3 rounded-full bg-[#2E7D32]" />
          ) : (
            <ChevronRight height={2} width={2} color="#6b7280" />
          )}
        </View>
      </Pressable>
    );
  };

  const renderSkeleton = () => (
    <View className="px-4 py-4">
      {Array.from({ length: 3 }).map((_, index) => (
        <View key={index} className="flex-row items-center gap-4 mb-6">
          <ShimmerBox width={48} height={48} variant="sphere" />
          <View className="flex-1">
            <ShimmerBox width="70%" height={16} borderRadius={4} />
          </View>
          <ShimmerBox width={12} height={12} variant="sphere" />
        </View>
      ))}
    </View>
  );

  return (
    <View>
      <Modal
        isVisible={shouldShowModal}
        onBackdropPress={close}
        onBackButtonPress={close}
        onModalHide={handleModalHide}
        animationIn="fadeIn"
        animationOut="fadeOut"
        animationInTiming={200}
        animationOutTiming={200}
        backdropTransitionInTiming={200}
        backdropTransitionOutTiming={1}
        backdropOpacity={0.5}
        statusBarTranslucent
        useNativeDriverForBackdrop
        avoidKeyboard
      >
        <View className="bg-white rounded-3xl overflow-hidden">
          <View className="flex-row items-center justify-between px-5 py-5">
            <Text className="text-black text-2xl font-semibold">Switch profile</Text>
            <Pressable onPress={close} hitSlop={10}>
              <Close height={2} width={2} color="#000" />
            </Pressable>
          </View>
          {loading ? (
            renderSkeleton()
          ) : (
            <FlatList
              data={combinedProfiles}
              keyExtractor={(item) => String(item.id)}
              renderItem={renderItem}
              ItemSeparatorComponent={() => <View className="h-4" />}
              contentContainerStyle={{ paddingHorizontal: 12, paddingBottom: 12, paddingTop: 4 }}
            />
          )}
        </View>
      </Modal>
    </View>
  );
};

export default SwitchProfileModal;
