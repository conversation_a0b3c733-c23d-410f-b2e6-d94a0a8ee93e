import type React from 'react';
import type { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import type { CompositeNavigationProp, NavigatorScreenParams } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type {
  FieldTypeI,
  ShipCreateEditPayloadI,
  ShipPreFilledDataTypeI,
} from '../screens/EditShipItem/components/EditShipItem/types';

export type ConnectionTypeI =
  | 'connections'
  | 'followers'
  | 'following'
  | 'mutuals'
  | 'requests_received'
  | 'requests_sent';

export type ContentType = 'USER_POST' | 'SCRAPBOOK_POST';

type PostIdParam = { postId: string };

export type AuthStackParamListI = {
  AddUserDetailScreen: undefined;
  CreateAccount: undefined;
  VerifyEmail: { email: string; profileId: string };
  Onboarding: undefined;
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
  };
  SetUsername: undefined;
  UserLogin: undefined;
  ForgotPassword: undefined;
  VerifyPasswordReset: { email: string };
  ResetPassword: { email: string };
  PrivacyPolicy: { options?: boolean; email?: string };
  PolicyAcceptance: undefined;
  ReferralCode: undefined;
};

export type AIStackParamListI = {
  AIAssistant: undefined;
};

export type HomeStackParamListI = {
  Comment: PostIdParam & { type?: ContentType; portUnLocode?: string };
  Connection: { profileId: string; type: ConnectionTypeI; isUserProfile?: boolean };
  GlobalSearch: undefined;
  Home: undefined;
  AIChat: undefined;
  Likes: PostIdParam & { type?: ContentType };
  EditExperienceList: { profileId?: string; editable?: boolean };
  EditSkillsList: { profileId?: string; category?: string; editable?: boolean };
  NotFound: undefined;
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  PortProfile: { unLocode: string; dataType: string };
  Chats: { profileId: string };
  Chat: { id: string };
  ShipProfile: { imo: string; dataType: string };
  Votes: { type: 'upvote' | 'downvote'; id: string };
  EditEducationList: { profileId?: string; editable?: boolean };
  EditCertificationList: {
    profileId?: string;
    editable?: boolean;
    tab?: 'statutory' | 'value-added';
  };
  EditShipItem: {
    shipId?: string;
    data?: ShipPreFilledDataTypeI[];
    field?: FieldTypeI;
    entityId?: string;
    fromProfileExperience?: {
      id: string;
      imo: string;
    };
    refetch?: () => void;
  };
  SavedPosts: undefined;
  EditDocumentList: { profileId?: string; editable?: boolean; tab?: 'identity' | 'visa' };
  LeaderBoard: undefined;
  PortsVisited: { profileId?: string };
  UserSettings: undefined;
  ReferralDetails: undefined;
  CreateEntityProfile: undefined;
  VerifyEntityEmail: { email: string; entityProfileId: string; emailType: string };
  CreateEntityProfileSuccess: { entityProfileId: string };
  VerificationReviewSentSuccess: undefined;
  EntityProfile: { entityProfileId?: string };
  EditEntityProfile: undefined;
  Follows: { entityProfileId: string; type: string };
};

export type ProfileStackParamsListI = {
  BlockedUserProfiles: undefined;
  Terms: undefined;
  Privacy: undefined;
  Comment: PostIdParam & { type?: ContentType; portUnLocode?: string };
  Connection: { profileId: string; type: ConnectionTypeI; isUserProfile?: boolean };
  Chat: { id: string };
  EditCargoItem: {
    profileId: string;
    experienceId?: string;
    shipId?: string;
    cargoId?: string;
    data: ShipCreateEditPayloadI[];
    refetch: () => void;
    isClientMode: boolean;
    shipData: { fromDate: string; toDate: string | null };
    shipTempId?: string;
  };
  EditCertificationItem: { profileId?: string; certificationId?: string; type: string };
  EditCertificationList: {
    profileId?: string;
    editable?: boolean;
    tab?: 'statutory' | 'value-added';
  };
  EditDocumentItem: { profileId?: string; documentId?: string; type?: string };
  EditDocumentList: { profileId?: string; editable?: boolean; tab?: 'identity' | 'visa' };
  EditEducationItem: { profileId?: string; educationId?: string };
  EditEducationList: { profileId?: string; editable?: boolean };
  EditEquipmentItem: {
    profileId: string;
    experienceId?: string;
    shipId?: string;
    equipmentId?: string;
    data: ShipCreateEditPayloadI[];
    refetch: () => void;
    isClientMode: boolean;
    shipTempId?: string;
  };
  EditExperienceItem: { profileId?: string; experienceId?: string };
  EditExperienceList: { profileId?: string; editable?: boolean };
  EditSkillsList: { profileId?: string; category?: string; editable?: boolean };
  EditUserProfile: undefined;
  Likes: PostIdParam & { type?: ContentType };
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  PortsVisited: { profileId?: string };
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
    multipleSelection?: boolean;
    searchWithoutInput?: boolean;
  };
  EditDetail: { type: string; action: 'add' | 'contribute'; text?: string };
  UserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  EditShipItem: {
    shipId?: string;
    data?: ShipPreFilledDataTypeI[];
    field?: FieldTypeI;
    entityId?: string;
    fromProfileExperience?: {
      id: string;
      imo: string;
    };
    refetch?: () => void;
  };
  ShipProfile: { imo: string; dataType: string };
  UserSettings: undefined;
  Scoreboard: undefined;
};

export type CreateStackParamsListI = {
  CreateContent: {
    type?: ContentType;
    portUnLocode?: string;
    editing?: boolean;
    postId?: string;
  };
};

export type NotificationStackParamsListI = {
  Notification: undefined;
  ForumAnswers: { postId: string };
  ForumComments: { postId: string; type: 'FORUM_QUESTION' | 'FORUM_ANSWER' };
  Forum: undefined;
  Likes: PostIdParam & { type?: ContentType };
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
  Comment: PostIdParam & { type?: ContentType; portUnLocode?: string };
};

export type LearnCollabStackParamsListI = {
  Forum: { activeTab?: 'questions' | 'communities' | 'news' } | undefined;
  ForumAnswers: { postId: string };
  ForumComments: { postId: string; type: 'FORUM_QUESTION' | 'FORUM_ANSWER' };
  ForumSearch: undefined;
  ForumFilter: undefined;
  CreateCommunity: undefined;
  CommunityRestrictions: undefined;
  People: {
    type: 'add' | 'create';
    title: string;
    btnText: string;
    communityId?: string;
    role?: 'ADMIN' | 'MODERATOR';
  };
  CommunityQuestion: { id: string; communityId?: string };
  CreateQuestion: { editing?: boolean; questionId?: string; communityId?: string } | undefined;
  ForumSetting: { communityId: string };
  CommunityMembers: { communityId?: string };
  CommunityBlocked: { communityId?: string };
  CommunityRequests: { communityId: string };
  Votes: { type: 'upvote' | 'downvote'; id: string };
  Community: { id: string; fromSearch?: boolean; fromCommunityTab?: boolean };
  MyCommunities: { removedCommunityId?: string } | undefined;
  Explore: undefined;
  ExploreQna: undefined;
  ExploreCommunities: undefined;
  ExploreTroubleShoot: undefined;
  ExploreForum:
    | {
        type?: 'NORMAL' | 'TROUBLESHOOT';
        topicId?: string;
        topicDataType?: 'master' | 'raw';
        equipmentCategory?: { id: string; dataType: 'master' | 'raw' };
        equipmentManufacturer?: { id: string; dataType: 'master' | 'raw' };
        equipmentModel?: { id: string; dataType: 'master' | 'raw' };
        fromExploreTroubleshoot?: boolean;
      }
    | undefined;
  ExploreNews:
    | {
        topicId?: string;
      }
    | undefined;
  SearchScreen: {
    title: string;
    placeholder: string;
    selectionKey: string;
    multipleSelection?: boolean;
    searchWithoutInput?: boolean;
    showDoneButton?:boolean;
  };
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
};

export type NearbyStackParamListI = {
  Nearby: undefined;
  EditAnnouncement: { announcementId?: string; refetch?: () => void };
  Location: { type: 'create' | 'self' | 'other' };
  NearbySettings: undefined;
  PeopleAttending: { announcementId: string };
  OtherUserProfile: {
    profileId?: string;
    fromTabPress?: boolean;
  };
};

export type CareerStackParamListI = {
  Careers: undefined;
  MyJobs: undefined;
  JobPosts: undefined;
  CareerDetails: { jobPostId: string };
  Applicants: { jobId: string };
  EditJobPost: { jobId?: string; editing?: boolean };
  CreateExternalJobPost : { type: string };
};

export type BottomTabParamListI = {
  CreateStack: NavigatorScreenParams<CreateStackParamsListI>;
  HomeStack: NavigatorScreenParams<HomeStackParamListI>;
  NotificationStack: NavigatorScreenParams<NotificationStackParamsListI>;
  LearnCollabStack: NavigatorScreenParams<LearnCollabStackParamsListI>;
  NearbyStack: NavigatorScreenParams<NearbyStackParamListI>;
  AIStack?: NavigatorScreenParams<AIStackParamListI>;
};

export type RootDrawerParamListI = {
  MainTabs: NavigatorScreenParams<BottomTabParamListI>;
  ProfileStack: NavigatorScreenParams<ProfileStackParamsListI>;
};

export type AppStackParamListI = AuthStackParamListI &
  HomeStackParamListI &
  NotificationStackParamsListI &
  CreateStackParamsListI &
  LearnCollabStackParamsListI &
  NearbyStackParamListI &
  ProfileStackParamsListI &
  AIStackParamListI;

export type RootStackParamListI = {
  auth: undefined;
  main: NavigatorScreenParams<RootDrawerParamListI>;
  splash: undefined;
};

export type BottomTabNavigationI = CompositeNavigationProp<
  BottomTabNavigationProp<BottomTabParamListI>,
  StackNavigationProp<AppStackParamListI>
>;

export type DrawerNavigationI = CompositeNavigationProp<
  DrawerNavigationProp<RootDrawerParamListI>,
  StackNavigationProp<AppStackParamListI>
>;

export type StackScreenI<T> = {
  name: keyof T;
  component: React.FC;
};

export interface TabIconProps {
  IconComponent: React.FC<{ fill?: string; stroke?: string }>;
  focused: boolean;
  tabName: string;
}

export interface CreateButtonProps {
  focused: boolean;
}

export interface TabBarIconProps {
  focused: boolean;
  color?: string;
  size?: number;
}

export type HomeScreenActionsRef = React.RefObject<{
  scrollToTop?: () => void;
  handleRefresh?: () => void;
}>;
