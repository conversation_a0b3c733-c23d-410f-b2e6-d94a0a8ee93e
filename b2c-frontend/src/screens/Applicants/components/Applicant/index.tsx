import { useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import BottomSheet from '@/src/components/Bottomsheet';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { formatElapsedTime } from '@/src/utilities/datetime';
import { showToast } from '@/src/utilities/toast';
import EditPencil from '@/src/assets/svgs/EditPencil';
import Match from '@/src/assets/svgs/Match';
import { updateApplicationStatus } from '@/src/networks/company/job/application';
import { ApplicantPropsI } from './types';

type ApplicationStatus = 'SHORTLISTED' | 'REJECTED_BY_ENTITY' | 'OFFERED';

const STATUS_OPTIONS = [
  { key: 'SHORTLISTED' as ApplicationStatus, label: 'Shortlist', textClassName: '' },
  {
    key: 'REJECTED_BY_ENTITY' as ApplicationStatus,
    label: 'Reject',
    textClassName: 'text-red-500',
  },
  { key: 'OFFERED' as ApplicationStatus, label: 'Offer', textClassName: '' },
] as const;

const Applicant = ({ applicant, onStatusChanged }: ApplicantPropsI) => {
  const timeAgo = formatElapsedTime(applicant.createdAt);
  const [sheetVisible, setSheetVisible] = useState(false);
  const [updating, setUpdating] = useState(false);

  const handleStatusChange = async (status: 'SHORTLISTED' | 'REJECTED_BY_ENTITY' | 'OFFERED') => {
    if (!applicant.id) {
      setSheetVisible(false);
      showToast({ message: 'Missing applicationId', type: 'error' });
      return;
    }
    try {
      setUpdating(true);
      await updateApplicationStatus({ applicationId: applicant.id, status });
      onStatusChanged?.(applicant.id);
      setSheetVisible(false);
      showToast({ message: 'Updated', type: 'success' });
    } catch (_e) {
      showToast({ message: 'Failed to update', type: 'error' });
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Pressable
      onPress={() => {}}
      className="flex-row gap-2 px-3 py-4 border-b border-[#BDBDBD] items-center justify-between"
    >
      <View className="flex-row gap-2 items-start justify-between w-full">
        <View className="flex-row gap-2 ">
          <UserAvatar
            avatarUri={applicant.ApplicantProfile.avatar}
            name={applicant.ApplicantProfile.name}
            width={RFPercentage(3.5)}
            height={RFPercentage(3.5)}
          />
          <View className="w-4/5">
            <Text className="font-base font-semibold">{applicant.ApplicantProfile.name}</Text>
            <Text className="leading-5">{`${applicant.ApplicantProfile?.designation?.name} ${applicant.ApplicantProfile?.entity?.id ? `at ${applicant.ApplicantProfile?.entity?.name}` : ''} `}</Text>
            <View className="flex-row items-center gap-2 my-1">
              <Text className="text-[#525252]">{timeAgo}</Text>
              <View className="flex-row items-center bg-backgroundGrayDark gap-1 rounded-full px-2 py-1.5 border-[#EAEAEA] border">
                <Match />
                <Text className="text-base font-semibold text-black">{applicant.matching}%</Text>
              </View>
            </View>
          </View>
        </View>
        <View className="flex-row items-center gap-3">
          <Pressable
            onPress={() => setSheetVisible(true)}
            accessibilityLabel="Edit status"
            accessibilityRole="button"
          >
            <EditPencil width={2.5} height={2.5} />
          </Pressable>
        </View>
      </View>
      <BottomSheet
        visible={sheetVisible}
        onClose={() => setSheetVisible(false)}
        onModalHide={() => {}}
      >
        <OptionsMenu>
          {STATUS_OPTIONS.map(({ key, label, textClassName }) => (
            <OptionItem
              key={key}
              label={label}
              textClassName={textClassName}
              onPress={() => handleStatusChange(key)}
              disabled={updating}
            />
          ))}
        </OptionsMenu>
        {updating && (
          <View className="flex-row items-center justify-center py-4">
            <ActivityIndicator />
            <Text className="ml-2 text-[#525252]">Updating...</Text>
          </View>
        )}
      </BottomSheet>
    </Pressable>
  );
};

export default Applicant;
