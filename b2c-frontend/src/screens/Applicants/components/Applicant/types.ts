import { IdNameI } from '@/src/types/common/data';
import { ProfileExternalI } from '@/src/networks/answerVote/types';
import { Dispatch, SetStateAction } from 'react';

export type ApplicantI = {
  ApplicantProfile: ProfileExternalI;
  DecisionMakerProfile: ProfileExternalI;
  cursorId: string | null;
  id: string;
  matching: number;
  status: string;
  createdAt: string;
};

export type ApplicantPropsI = {
  applicant: ApplicantI;
  onStatusChanged?: (applicationId: string) => void
};
