import { SetStateAction, useCallback, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import CareerFilterBar from '@/src/components/CareerFilterBar';
import Tabs from '@/src/components/Tabs';
import Applicant from '../Applicant';
import { ApplicantsPropsI } from './types';
import { useApplicants } from './useHook';

const Applicants = ({ onBack, jobId }: ApplicantsPropsI) => {
  const [activeTab, setActiveTab] = useState('applied');

  const tabs = [
    { id: 'applied', label: 'Applied' },
    { id: 'shortlisted', label: 'Shortlisted' },
    { id: 'accepted', label: 'Accepted' },
    { id: 'rejected', label: 'Rejected' },
  ];

  const {
    applicants,
    searchText,
    filterTabs,
    loading,
    isLoadingMore,
    loadingFilters,
    setSearchText,
    loadMoreApplicants,
    fetchApplicants,
    onFilterPress,
    handleStatusChange
  } = useApplicants(activeTab, jobId);

  const onRefresh = useCallback(() => {
    fetchApplicants(false,true);
  }, []);

  const renderFooter = () => (
    <View className="pb-4">
      {isLoadingMore && <ActivityIndicator size="small" className="my-4" />}
    </View>
  );

  return (
    <View className="flex-1">
      <BackButton onBack={onBack} label="Applicants" />
      <Tabs
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
      />
      <CareerFilterBar
        page="applicants"
        ellipsesVisible={false}
        onSearchTextChange={setSearchText}
        searchTextValue={searchText}
        className="py-5"
        filterTabs={filterTabs}
        onFilterPress={onFilterPress}
        onApplyPress={() => fetchApplicants(false, true)}
        loading={loadingFilters}
      />

      <FlatList
        data={applicants}
        renderItem={({ item }) => <Applicant applicant={item}onStatusChanged={handleStatusChange}/>}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        onEndReached={loadMoreApplicants}
        onEndReachedThreshold={0.8}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl refreshing={loading} onRefresh={onRefresh} tintColor="#000" />
        }
      />
    </View>
  );
};

export default Applicants;
