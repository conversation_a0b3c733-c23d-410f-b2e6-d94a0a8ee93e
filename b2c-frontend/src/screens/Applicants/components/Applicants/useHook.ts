import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IdLabelAppliedCountI } from '@/src/components/CareerFilterBar/types';
import { selectActiveFilterNumbers, selectActiveFilters } from '@/src/redux/selectors/careers';
import { setFilters } from '@/src/redux/slices/career/careerSlice';
import { fetchApplicantsForJobPostsAPI } from '@/src/networks/jobs/fetchApplicantsForJobPosts';
import { fetchFiltersForEntityMemberForApplicantsAPI } from '@/src/networks/jobs/fetchFiltersForEntityMemberForApplicants';
import { ApplicantI } from '../Applicant/types';
import { showToast } from '@/src/utilities/toast';

const PAGE_SIZE = 10;

export const useApplicants = (type: string, jobId: string) => {
  const dispatch = useDispatch();
  const filters = useSelector(selectActiveFilters('applicants'));
  const [applicants, setApplicants] = useState<ApplicantI[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingFilters,setLoadingFilters] = useState(false);  
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [cursorId, setCursorId] = useState<string | null>(null);
  const activeFilterNumbers = useSelector(selectActiveFilterNumbers('applicants'));
  const filterTabs: IdLabelAppliedCountI[] = [
    {
      id: 'location',
      label: 'Location',
      appliedCount: activeFilterNumbers.locations || 0,
    },
    {
      id: 'designation',
      label: 'Designation',
      appliedCount: activeFilterNumbers.designations || 0,
    },
    {
      id: 'yoe',
      label: 'Year(s) of Experience',
      appliedCount: activeFilterNumbers.yearOfExperiences || 0,
    },
  ];

  const fetchApplicants = async (loadMore = false, reset = false) => {
    try {
      if (reset) {
        setCursorId(null);
        setApplicants([]);
        setLoading(true);
      } else if (loadMore) {
        if (!cursorId || !hasMore || !isLoadingMore) return;
        setIsLoadingMore(true);
      } else {
        setLoading(true);
      }

      const query = {
        cursorId: reset ? null : cursorId,
        pageSize: PAGE_SIZE,
        status: transformStatus(type),
        jobId,
      };

      const body = {
        designations: filters.designations,
        countries: filters.locations,
        yearsOfExperiences: filters.yearsOfExperiences,
      };
      const result = await fetchApplicantsForJobPostsAPI(query, body);
      if (loadMore) {
        setApplicants((prev) => [...prev, ...result.data]);
      } else {
        setApplicants(result.data);
      }

      setCursorId(result.nextCursorId);
      setHasMore(result.nextCursorId !== null);
    } catch (error) {
    } finally {
      if (loadMore) {
        setIsLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    const fetchInitialApplicants = async () => {
      try {
        setLoading(true);
        const query = {
          cursorId: null,
          pageSize: PAGE_SIZE,
          status: transformStatus(type),
          jobId,
        };

        const body = {
          designations: filters.designations,
          countries: filters.locations,
          yearsOfExperiences: filters.yearOfExperiences,
        };
        const result = await fetchApplicantsForJobPostsAPI(query, body);
        setApplicants(result.data);

        setCursorId(result.nextCursorId);
        setHasMore(result.nextCursorId !== null);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };
    fetchInitialApplicants();
  }, [type]);

  const loadMoreApplicants = () => {
    if (!cursorId || !hasMore || isLoadingMore) return;
    fetchApplicants(true);
  };

  const onFilterPress = async () => {
    try{
      setLoadingFilters(true)
      const query = {
        jobId,
        status: transformStatus(type),
      };
      const filters = await fetchFiltersForEntityMemberForApplicantsAPI(query);
      dispatch(
        setFilters({
          page: 'applicants',
          filters: filters,
        }),
      );
    }catch(e){
      showToast({
        type:'error',
        message:'Failed to load Filters.Try Again Later'
      })
    }
    finally{
      setLoadingFilters(false)
    }
  };

  const handleStatusChange = (applicationId:string) => {
    setApplicants(prev => prev.filter(item => item.id !== applicationId))
  }

  return {
    applicants,
    searchText,
    filterTabs,
    loading,
    isLoadingMore,
    loadingFilters,
    setSearchText,
    loadMoreApplicants,
    fetchApplicants,
    onFilterPress,
    handleStatusChange
  };
};

const transformStatus = (type: string) => {
  const status = {
    applied: 'PENDING',
    shortlisted: 'SHORTLISTED',
    accepted: 'ACCEPTED_BY_APPLICANT',
    rejected: 'REJECTED_BY_ENTITY',
  };

  return status[type as keyof typeof status];
};
