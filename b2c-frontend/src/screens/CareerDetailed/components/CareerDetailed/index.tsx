import React, { useEffect, useMemo, useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import BottomSheet from '@/src/components/Bottomsheet';
import Button from '@/src/components/Button';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentEntityProfile } from '@/src/redux/selectors/entityProfile';
import { formatElapsedTime } from '@/src/utilities/datetime';
import { showToast } from '@/src/utilities/toast';
import { CareerStackParamListI } from '@/src/navigation/types';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import EditPencil from '@/src/assets/svgs/EditPencil';
import Match from '@/src/assets/svgs/Match';
import { fetchJobForCandidate } from '@/src/networks/company/job/job';
import { closeJob } from '@/src/networks/company/job/job';
import { JobHeadPropsI } from './types';
import useJobActions from './useHook';
import Accordion from '@/src/components/Accordion';
import CareerDetailedAccordion from '../CareerDetailedAccordion';

const CareerDetailed: React.FC<JobHeadPropsI> = ({
  jobId
}) => {

  const { 
    handleApply,
    handleOptions,
    handleWithdraw,
    onWithdrawPress,
    handleApplications,
    setClosing,
    setIsClosed,
    setCloseModalVisible,
    setConfirmAction,
    setOptionsVisible,
    setOpenWithdrawAfterSheet,
    isApplied,
    setIsApplied,
    withdrawing,
    applicationId,
    isApplicantsVisible,
    optionsVisible,
    closeModalVisible,
    closing,
    confirmAction,
    openWithdrawAfterSheet,
    isClosed,
    displayTitle,
    displayCompanyName,
    displayMatchPercent,
    displayPostedAgoText,
    displayLocationText,
    displayAboutText,
    displayRolesResponsibilitiesText,
    displayRequirementsText,
    displayBenefitsText,
    loading,
    certificates,
    documents,
    cargos,
    skills,
    otherRequirements,
    benefits,
    equipments,
    experiences,
    showShipDetails,
    shipType,
    shipImo,
    jobType,
    genderEquityIndex,
    showFields,
    navigation,
    joiningDate
  } = useJobActions({
    jobId,
  });

  if(loading) return <ActivityIndicator />

  const CertificateChildren = () => {
    return (
      <View className='bg-white'>
        <View className='flex-row items-center justify-between p-2 bg-[#D9D9D9] rounded-t-lg'>
          <Text className="font-semibold">Certification</Text>
          <Text className="font-semibold">Mandatory</Text>
        </View>
        {certificates.map((certificate, index) => {
          const certificateMaster = certificate.certificateCourseId ? true : false
          const isLast = index === certificates.length - 1;
          
          return (
            <View 
              key={index} 
              className={`flex-row items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
            >
              <Text className="text-gray-800">
                {certificateMaster ? certificate.CertificateCourse?.name : certificate.CertificateCourseRawData?.name}
              </Text>
              <View className={`px-3 py-1 rounded-full ${certificate.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}>
                <Text className={`text-sm font-medium ${certificate.isMandatory ? 'text-green-800' : 'text-gray-600'}`}>
                  {certificate.isMandatory ? 'Yes' : 'No'}
                </Text>
              </View>
            </View>
          )
        })}
      </View>
    )
  }

  const EquipmentChildren = () => {
    return(
      <View className='bg-white p-5'>
        <View className="border-x-0 border-b-0 border-gray-200">
          {
            equipments.map((equ, index) => {
              const equCategoryMaster = equ.equipmentCategoryId ? true : false
              const equModelMaster = equ.equipmentModelId ? true : false
              const equManufacturerMaster = equ.equipmentManufacturerId ? true : false
              const fuelTypeMaster = equ.fuelTypeId ? true : false
              const isLast = index === equipments.length - 1;
              return(
                <View 
                  key={index} 
                  className={` py-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
                >
                  <View className='flex-row items-center justify-between'>
                    <View>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Equipment Category:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Equipment Manufacturer:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Equipment Model:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Fuel type:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Month(s) of Experience:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Mandatory:</Text>
                    </View>
                    <View>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{equCategoryMaster ? equ.EquipmentCategory?.name : equ.EquipmentCategoryRawData?.name}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{equManufacturerMaster ? equ.EquipmentManufacturer?.name : equ.EquipmentManufacturerRawData?.name}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{equModelMaster ? equ.EquipmentModel?.name : equ.EquipmentModelRawData?.name}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{fuelTypeMaster ? equ.FuelType?.name : equ.FuelTypeRawData?.name}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{equ.monthsOfExperience}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{equ.isMandatory ? 'Yes' : 'No'}</Text>
                    </View>
                  </View>
                </View>
              )
            })
          }
        </View>
      </View>
    )
  }

  const ExperienceChildren = () => {
    return(
      <View className='bg-white p-5'>
        <View className="border-x-0 border-b-0 border-gray-200">
          {
            experiences.map((exp, index) => {
              const desigMaster = exp.designationAlternativeId ? true : false
              const shipTypeMaster = exp.SubVesselType ? true : false
              const showDesig = exp.isTotal
              const isLast = index === equipments.length - 1;
              return(
                <View 
                  key={index} 
                  className={` py-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
                >
                  <View className='flex-row items-center justify-between'>
                    <View>
                      {showDesig && <Text className="text-gray-800 font-medium flex-1 mr-3">Designation:</Text>}
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Ship Type:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Month(s) of Experience:</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">Mandatory:</Text>
                    </View>
                    <View>
                      {showDesig && <Text className="text-gray-800 font-medium flex-1 mr-3">{desigMaster ? exp.DesignationAlternative?.name : exp.DesignationRawData?.name}</Text>}
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{shipTypeMaster ? exp.SubVesselType?.name : exp.SubVesselTypeRawData?.name}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{exp.monthsOfExperience}</Text>
                      <Text className="text-gray-800 font-medium flex-1 mr-3">{exp.isMandatory ? 'Yes' : 'No'}</Text>
                    </View>
                  </View>
                </View>
              )
            })
          }
        </View>
      </View>
    )
  }

  const DocumentChildren = () => {
    return(
      <View className='bg-white'>
        <View className='flex-row items-center justify-between p-2 bg-[#D9D9D9] rounded-t-xl'>
          <Text className="font-semibold">Document</Text>
          <Text className="font-semibold">Countries</Text>
          <Text className="font-semibold">Mandatory</Text>
        </View>
        {
          documents.map((doc,index) => {
            const documentMaster = doc.documentTypeId ? true : false
            const isLast = index === certificates.length - 1;
            return(
              <View 
                key={index} 
                className={`flex-row items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
              >
                <Text className="text-gray-800">
                  {documentMaster ? doc.DocumentType?.name : doc.DocumentTypeRawData?.name}
                </Text>
                <View className='flex-row'>
                  {
                    doc.countries.map((country) => (
                      <Text>{country}</Text>
                    ))
                  }
                </View>
                <View className={`px-3 py-1 rounded-full ${doc.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}>
                  <Text className={`text-sm font-medium ${doc.isMandatory ? 'text-green-800' : 'text-gray-600'}`}>
                    {doc.isMandatory ? 'Yes' : 'No'}
                  </Text>
                </View>
              </View>
            )
        })
        }
      </View>
    )
  }

  const CargoChildren = () => {
    return(
      <View className='bg-white'>
        <View className='flex-row items-center justify-between p-2 bg-[#D9D9D9] rounded-t-xl'>
          <Text className="font-semibold">Name</Text>
          <Text className="font-semibold">{`Month(s) of Experience`}</Text>
          <Text className="font-semibold">Mandatory</Text>
        </View>
        {
          cargos.map((crg,index) => {
            const isLast = index === cargos.length - 1;
            return(
              <View 
                key={index} 
                className={`flex-row items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
              >
                <Text className="text-gray-800">
                  {crg.name}
                </Text>
                <Text className="text-gray-800">
                  {crg.monthsOfExperience}
                </Text>
                <View className={`px-3 py-1 rounded-full ${crg.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}>
                  <Text className={`text-sm font-medium ${crg.isMandatory ? 'text-green-800' : 'text-gray-600'}`}>
                    {crg.isMandatory ? 'Yes' : 'No'}
                  </Text>
                </View>
              </View>
            )
        })
        }
      </View>
    )
  }

  const OtherEquipmentChildren = () => {
    return(
      <View className='bg-white'>
        <View className='flex-row items-center justify-between p-2 bg-[#D9D9D9] rounded-t-xl'>
          <Text className="font-semibold flex-1">Details</Text>
          <Text className="font-semibold w-20 text-center">Mandatory</Text>
        </View>
        {
          otherRequirements.map((oth,index) => {
            const isLast = index === otherRequirements.length - 1;
            return(
              <View 
                key={index} 
                className={`flex-row items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
              >
                <Text 
                  className="text-gray-800 flex-1 pr-4" 
                  numberOfLines={2} 
                  ellipsizeMode="tail"
                >
                  {oth.details}
                </Text>
                <View className={`px-3 py-1 rounded-full ${oth.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}>
                  <Text className={`text-sm font-medium ${oth.isMandatory ? 'text-green-800' : 'text-gray-600'}`}>
                    {oth.isMandatory ? 'Yes' : 'No'}
                  </Text>
                </View>
              </View>
            )
          })
        }
      </View>
    )
  }

  const SkillChildren = () => {
    return (
      <View className='bg-white'>
        <View className='flex-row items-center justify-between p-2 bg-[#D9D9D9] rounded-t-lg'>
          <Text className="font-semibold">Skill</Text>
          <Text className="font-semibold">Mandatory</Text>
        </View>
        {skills.map((skl, index) => {
          const skillMaster = skl.skillId ? true : false
          const isLast = index === certificates.length - 1;
          
          return (
            <View 
              key={index} 
              className={`flex-row items-center justify-between p-3 ${!isLast ? 'border-b border-gray-100' : ''}`}
            >
              <Text className="text-gray-800">
                {skillMaster ? skl.Skill?.name : skl.SkillRawData?.name}
              </Text>
              <View className={`px-3 py-1 rounded-full ${skl.isMandatory ? 'bg-green-100' : 'bg-gray-100'}`}>
                <Text className={`text-sm font-medium ${skl.isMandatory ? 'text-green-800' : 'text-gray-600'}`}>
                  {skl.isMandatory ? 'Yes' : 'No'}
                </Text>
              </View>
            </View>
          )
        })}
      </View>
    )
  }

  const BenefitChildren = () => {
    const { internetDetails, insuranceDetails, salaryDetails, contractDetails } = benefits || {};
    
    const benefitSections = [
      {
        title: 'Internet',
        data: internetDetails,
        entries: Object.entries(internetDetails ?? {}).filter(([_, value]) => 
          value !== undefined && value !== null && value !== ''
        ),
        formatter: (key: string, value: any) => {
          if (key === 'internetAvailable') return value ? 'Available' : 'Not Available';
          if (key === 'internetSpeed') return `${value} Mbps`;
          if (key === 'internetLimit') return `${value} MB/day`;
          return value;
        }
      },
      {
        title: 'Insurance',
        data: insuranceDetails,
        entries: Object.entries(insuranceDetails ?? {}).filter(([_, value]) => 
          value !== undefined && value !== null && value !== ''
        ),
        formatter: (key: string, value: any) => {
          if (key === 'familyOnboard') return value ? 'Yes' : 'No';
          return value;
        }
      },
      {
        title: 'Salary',
        data: salaryDetails,
        shouldShow: salaryDetails?.showSalary !== false,
        entries: Object.entries(salaryDetails ?? {}).filter(([key, value]) => {
          if (salaryDetails?.showSalary === false) return false;
          return value !== undefined && value !== null && value !== '';
        }),
        formatter: (key: string, value: any) => {
          if (key === 'showSalary') return value ? 'Shown' : 'Hidden';
          if (key === 'minSalary' || key === 'maxSalary') return `${value}`;
          return value;
        }
      },
      {
        title: 'Contract',
        data: contractDetails,
        entries: Object.entries(contractDetails ?? {}).filter(([_, value]) => 
          value !== undefined && value !== null
        ),
        formatter: (key: string, value: any) => {
          if (key === 'contractDays') return `${value} days`;
          if (key === 'contractMonths') return `${value} months`;
          return value;
        }
      }
    ].filter(section => section.entries.length > 0 && (section.title !== 'Salary' || section.shouldShow));

    if (benefitSections.length === 0) return null;

    return (
      <View className='bg-white'>
        {benefitSections.map((section, sectionIndex) => (
          <View key={section.title} className={sectionIndex > 0 ? 'mt-6' : ''}>
            <Text className="font-semibold bg-[#D9D9D9] rounded-t-lg p-2 text-center">
              {section.title}
            </Text>
            <View className="border-x-0 border-b-0 border-gray-200">
              {section.entries.map(([key, value], index) => {
                const isLast = index === section.entries.length - 1;
                const formattedKey = key
                  .replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase())
                  .replace(/(internet|insurance|salary|contract)/gi, '')
                  .trim();

                const formattedValue = section.formatter(key, value);

                return (
                  <View 
                    key={key}
                    className={`flex-row justify-between items-start p-4 ${!isLast ? 'border-b border-gray-100' : ''}`}
                  >
                    <Text className="text-gray-800 font-medium flex-1 mr-3">{formattedKey}:</Text>
                    <Text 
                      className="text-gray-600 flex-1 text-right" 
                      numberOfLines={3} 
                      ellipsizeMode="tail"
                    >
                      {formattedValue}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>
        ))}
      </View>
    );
  };

  return (
    <View className="p-4">
      <View className="flex-row items-center justify-between mb-1">
        <View className="flex-row gap-2 items-center">
          <UserAvatar avatarUri={null} name={displayCompanyName!} width={20} height={20} />
          <Text className="text-xs font-normal">{displayCompanyName}</Text>
        </View>
        <View className="flex-row items-center gap-3">
          {isApplicantsVisible && (
            <Pressable onPress={() => navigation.navigate('EditJobPost', { jobId, editing: true })}>
              <EditPencil width={2.5} height={2.5} stroke="#666363" />
            </Pressable>
          )}
          {!isApplicantsVisible && (
            <Pressable onPress={() => handleOptions()}>
              <HorizontalEllipsis />
            </Pressable>
          )}
        </View>
      </View>

      <Text className="font-semibold text-xl mb-2">{displayTitle}</Text>

      <View className="flex-row items-center gap-2 mb-4">
        <View className="flex-row items-center">
          <Text className="text-[#666363] text-sm">{displayLocationText}</Text>
          <Text className="text-[#666363]">{' • '}</Text>
          <Text className="text-[#666363] text-sm">{displayPostedAgoText}</Text>
        </View>
        <View className="flex-row items-center bg-backgroundGrayDark gap-1 rounded-full px-2 py-1.5 border-[#EAEAEA] border">
          <Match />
          <Text className="text-lg font-semibold text-black">{displayMatchPercent}%</Text>
        </View>
      </View>

      {/* Actions row */}
      {isApplicantsVisible ? (
        <View className="flex-row gap-3 mb-2">
          <Button
            label="View applicants"
            variant="outline"
            onPress={handleApplications}
            className="flex-1 rounded-lg"
          />
          {isClosed ? (
            <View className="flex-1 rounded-lg border border-gray-300 items-center justify-center">
              <Text className="text-gray-600">Closed</Text>
            </View>
          ) : (
            <Button
              label="Close job"
              variant="outline"
              onPress={() => {
                setConfirmAction('close');
                setCloseModalVisible(true);
              }}
              className="flex-1 rounded-lg"
            />
          )}
        </View>
      ) : (
        <View className="flex-row gap-3 mb-2">
          <Button
            label={isApplied ? 'Applied' : 'Apply'}
            variant={isApplied ? 'primaryOutline' : 'primary'}
            onPress={() => {
              if (isApplied) return;
              handleApply();
            }}
            disabled={isApplied}
            className="flex-1 rounded-lg"
          />
          {/* <Button
            label="Save"
            variant="outline"
            onPress={() => {}}
            className={`flex-1 rounded-lg`}
          /> */}
        </View>
      )}

      <View className="bg-white rounded-lg p-4 border border-gray-100">
        {showShipDetails && (
          <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
            <Text className="text-gray-700 font-medium">Ship IMO</Text>
            <Text className="text-gray-900 font-semibold">{shipImo}</Text>
          </View>
        )}
        <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
          <Text className="text-gray-700 font-medium">Ship Type</Text>
          <Text className="text-gray-900 font-semibold">{shipType}</Text>
        </View>
        <View className="flex-row justify-between items-center py-3 border-b border-gray-100">
          <Text className="text-gray-700 font-medium">Job Type</Text>
          <Text className="text-gray-900 font-semibold">{jobType}</Text>
        </View>
        <View className="flex-row justify-between items-center py-3">
          <Text className="text-gray-700 font-medium">Gender Equity Index</Text>
          <Text className="text-gray-900 font-semibold">{genderEquityIndex}</Text>
        </View>
        {
          joiningDate && (
            <View className="flex-row justify-between items-center py-3">
              <Text className="text-gray-700 font-medium">Joining Date</Text>
              <Text className="text-gray-900 font-semibold">{joiningDate.split('T')[0]}</Text>
            </View>
          )
        }
      </View>

      <View className="mt-4">
        <Text className="text-base font-semibold mb-2">About</Text>
        <Text className="text-[#333333] text-sm leading-5 mb-4">{displayAboutText}</Text>

        <Text className="text-base font-semibold mb-2">Roles and responsibilities</Text>
        <View className="mb-4">
          {displayRolesResponsibilitiesText && displayRolesResponsibilitiesText.split('.').map(r => r.trim()).filter(r => r.length > 0).map((r, idx) => (
            <View key={`role-${idx}`} className="flex-row items-start mb-1">
              <Text className="text-[#333333] text-sm mr-2">{'•'}</Text>
              <Text className="text-[#333333] text-sm flex-1">{r}</Text>
            </View>
          ))}
        </View>

        {
          displayRequirementsText && (
            <>
              <Text className="text-base font-semibold mb-2">Requirements</Text>
              <View className="mb-4">
                {displayRequirementsText.split('.').map(r => r.trim()).filter(r => r.length > 0).map((req, idx) => (
                  <View key={`req-${idx}`} className="flex-row items-start mb-1">
                    <Text className="text-[#333333] text-sm mr-2">{'•'}</Text>
                    <Text className="text-[#333333] text-sm flex-1">{req}</Text>
                  </View>
                ))}
              </View>
            </>
          )
        }
        {
          displayBenefitsText && (
            <>
              <Text className="text-base font-semibold mb-2">Benefits</Text>
              <View className="mb-6">
                {displayBenefitsText.split('.').map(b => b.trim()).filter(b => b.length > 0).map((ben, idx) => (
                  <View key={`ben-${idx}`} className="flex-row items-start mb-1">
                    <Text className="text-[#333333] text-sm mr-2">{'•'}</Text>
                    <Text className="text-[#333333] text-sm flex-1">{ben}</Text>
                  </View>
                ))}
              </View>
            </>
          )
        }
      </View>

      {
        showFields &&
        <View className='gap-5'>
          <CareerDetailedAccordion
          label='Certification'
          >
            <CertificateChildren />
          </CareerDetailedAccordion>

          <CareerDetailedAccordion
          label='Experience'
          >
            <ExperienceChildren />
          </CareerDetailedAccordion>

          <CareerDetailedAccordion
          label='Equipment'
          >
            <EquipmentChildren />
          </CareerDetailedAccordion>

          <CareerDetailedAccordion
          label='Document'
          >
            <DocumentChildren />
          </CareerDetailedAccordion>
      
          <CareerDetailedAccordion
          label='Cargo'
          >
            <CargoChildren />
          </CareerDetailedAccordion>

          <CareerDetailedAccordion
          label='Skills'
          >
            <SkillChildren />
          </CareerDetailedAccordion>
          
          <CareerDetailedAccordion
          label='Other Equipments'
          >
            <OtherEquipmentChildren />
          </CareerDetailedAccordion>

          <CareerDetailedAccordion
          label='Benefits'
          >
            <BenefitChildren />
          </CareerDetailedAccordion>
        </View>
      }

      <BottomSheet
        visible={optionsVisible}
        onClose={() => setOptionsVisible(false)}
        onModalHide={() => {
          if (openWithdrawAfterSheet) {
            setOpenWithdrawAfterSheet(false);
            setCloseModalVisible(true);
          }
        }}
      >
        <OptionsMenu>
          <OptionItem label="Withdraw application" onPress={onWithdrawPress} />
        </OptionsMenu>
      </BottomSheet>

      <CustomModal
        isVisible={closeModalVisible}
        title={
          confirmAction === 'withdraw'
            ? 'Are you sure you want to withdraw your application?'
            : 'Are you sure you want to close this job?'
        }
        cancelText="Cancel"
        confirmText={confirmAction === 'withdraw' ? 'Withdraw' : 'Close job'}
        confirmButtonVariant="danger"
        isConfirming={confirmAction === 'withdraw' ? withdrawing : closing}
        onConfirm={async () => {
          if (!jobId) return;
          if (confirmAction === 'withdraw') {
            await handleWithdraw();
            setCloseModalVisible(false);
            setConfirmAction(null);
            return;
          }
          try {
            setClosing(true);
            await closeJob(jobId);
            setIsClosed(true);
            setCloseModalVisible(false);
            showToast({ message: 'Job closed', type: 'success' });
          } catch (_e) {
            showToast({ message: 'Failed to close job', type: 'error' });
          } finally {
            setClosing(false);
          }
        }}
        onCancel={() => {
          setCloseModalVisible(false);
          setConfirmAction(null);
        }}
      />
    </View>
  );
};

export default CareerDetailed;
