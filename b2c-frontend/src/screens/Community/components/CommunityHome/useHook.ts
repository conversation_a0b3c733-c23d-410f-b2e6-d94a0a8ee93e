import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch, useSelector } from 'react-redux';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { ForumPostProps } from '@/src/screens/Forum/components/ForumPost/types';
import { transformQuestionToForumPost } from '@/src/screens/Forum/components/ForumPost/utils';
import { CommunityI } from '@/src/screens/MyCommunities/components/CommunityList/types';
import { fetchCommunityById } from '@/src/networks/community/fetchOne';
import { leaveCommunityAPI } from '@/src/networks/community/member';
import {
  createCommunityRequestAPI,
  revokeCommunityRequestAPI,
} from '@/src/networks/community/request';
import { deleteCommunityAPI } from '@/src/networks/community/update';
import { fetchForumQuestionsAPI } from '@/src/networks/question/question';
import { ForumQuestionResultI } from '@/src/networks/question/types';
import { RoleType } from '../CommunityHomeHead/types';

const useCommunityHome = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'Community'>>();
  const { id: communityId } = route.params;
  const isFromSearch = route.params?.fromSearch === true;
  const profileId = useSelector((state: RootState) => state.user.profileId);

  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [nextCursorDate, setNextCursorDate] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [community, setCommunity] = useState<CommunityI | null>(null);
  const [posts, setPosts] = useState<ForumPostProps[]>([]);
  const [canView, setCanView] = useState<boolean>(true);

  const handleSelectPost = useCallback(
    async (post: { postId: string }) => {
      await dispatch(fetchForumQuestionDetail({ questionId: post.postId }));
      navigation.navigate('ForumAnswers', { postId: post.postId });
    },
    [dispatch, navigation],
  );

  const computeConnectionState = (
    data: CommunityI | null,
  ): 'connected' | 'requested' | 'disconnected' | null => {
    if (!data) return null;
    if (data.role) return 'connected';
    switch (data.requestStatus) {
      case 'PENDING':
        return 'requested';
      case 'ACCEPTED':
      case 'PARTIALLY_ACCEPTED':
        return 'disconnected';
      default:
        return 'disconnected';
    }
  };

  const displayError = useMemo(() => {
    if (!error) return null;
    const looksSensitive =
      /(\/backend\/api\/v\d+\/[^\s]+)|\bRoute\s+(GET|POST|PUT|DELETE)\b|https?:\/\/[^\s]+/i.test(
        error,
      );
    const msg = looksSensitive ? 'Failed to load. Please try again.' : error;
    if (__DEV__) console.log('[community:error raw]', error);
    return msg;
  }, [error]);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const communityRes = await fetchCommunityById(communityId);
      setCommunity(communityRes);

      const canViewNow = communityRes.access !== 'PRIVATE' || Boolean(communityRes.role);
      setCanView(canViewNow);

      if (!canViewNow) {
        setPosts([]);
        setNextCursorDate(null);
        setHasMore(false);
        return;
      }

      const questionsRes = await fetchForumQuestionsAPI({
        type: 'ALL',
        pageSize: '10',
        cursorDate: null,
        communityId,
      });
      const forumPosts = questionsRes.data.map((question) =>
        transformQuestionToForumPost(question as ForumQuestionResultI, question.media),
      );
      setPosts(forumPosts as ForumPostProps[]);
      setNextCursorDate(questionsRes.nextCursorDate);
      setHasMore(Boolean(questionsRes.nextCursorDate));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load community');
    } finally {
      setLoading(false);
    }
  }, [communityId]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      fetchData();
    });
    return unsubscribe;
  }, [navigation, fetchData]);

  const handleAddMembersNavigate = () => {
    navigation.navigate('People', {
      type: 'add',
      title: `Add members to ${community?.name}`,
      btnText: 'Add',
      communityId,
    });
  };

  const handleDeleteCommunity = async () => {
    await deleteCommunityAPI({ id: communityId });
    if (isFromSearch) navigation.goBack();
     else navigation.navigate('Forum', { activeTab: 'communities' });
  };

  const handleLoadMore = async () => {
    if (loading || loadingMore || !hasMore || !nextCursorDate) return;
    try {
      if (!canView || !nextCursorDate) return;
      setLoadingMore(true);
      const res = await fetchForumQuestionsAPI({
        type: 'ALL',
        pageSize: '10',
        cursorDate: nextCursorDate,
        communityId,
      });
      const morePosts = res.data.map((q) =>
        transformQuestionToForumPost(q as ForumQuestionResultI, q.media),
      );
      setPosts((prev) => [...prev, ...(morePosts as ForumPostProps[])]);
      setNextCursorDate(res.nextCursorDate);
      setHasMore(res.total > posts.length);
    } finally {
      setLoadingMore(false);
    }
  };

  const handleConnect = async (role: RoleType = 'MEMBER') => {
    if (!profileId) return;
    try {
      const res = await createCommunityRequestAPI({ communityId, profileId, requestedType: role });
      setCommunity((prev) =>
        prev ? ({ ...prev, requestStatus: res.status } as CommunityI) : prev,
      );
    } catch (err) {
      throw new Error('Failed to connect');
    }
  };

  const handleRevoke = async () => {
    if (!profileId) return;
    try {
      await revokeCommunityRequestAPI({ communityId, profileId });
      setCommunity((prev) => (prev ? { ...prev, requestStatus: null } : prev));
    } catch (err) {
      throw new Error('Failed to revoke');
    }
  };

  const handleLeave = async () => {
    if (!profileId) return;
    try {
      await leaveCommunityAPI({ communityId, profileId });
      setCommunity((prev) =>
        prev
          ? {
            ...prev,
            role: null as any,
            requestStatus: null,
            memberCount: Math.max(0, (prev.memberCount ?? 1) - 1),
          }
          : prev,
      );
    } catch (e: any) {
      if (e?.status && e?.code) {
        const code = e.code;
        const description = typeof e.message === 'string' ? e.message : 'Failed to leave community';
        if (code === 'FMMB003') {
          throw new Error(`'You are the last admin of this community.' `);
        } else {
          throw new Error(description);
        }
      } else {
        const description =
          typeof e?.message === 'string' ? e.message : 'Failed to leave community';
        showToast({ type: 'error', message: 'Leave failed', description });
      }
    }
  };

  const retry = () => {
    setError(null);
    fetchData();
  };

  return {
    loading,
    community,
    posts,
    handleSelectPost,
    handleAddMembersNavigate,
    handleDeleteCommunity,
    handleLoadMore,
    hasMore,
    loadingMore,
    error: displayError,
    retry,
    connectionState: computeConnectionState(community),
    handleConnect,
    handleRevoke,
    handleLeave,
    isFromSearch,
  };
};

export default useCommunityHome;
