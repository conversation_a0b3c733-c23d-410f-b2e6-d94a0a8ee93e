import { useState, useEffect } from 'react';
import { ActivityIndicator, Pressable, Text, View, Share } from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet';
import CustomModal from '@/src/components/Modal';
import { OptionsMenu, OptionItem } from '@/src/components/OptionsMenu';
import UserAvatar from '@/src/components/UserAvatar';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import AddItem from '@/src/assets/svgs/AddItem';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import Logout from '@/src/assets/svgs/Logout';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import Settings from '@/src/assets/svgs/Settings';
import { default as ShareIcon } from '@/src/assets/svgs/Share';
import Tick from '@/src/assets/svgs/Tick';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { CommunityHomeHeadPropsI, RoleType } from './types';
import { formatMemberCount } from './utils';

const ROLES: RoleType[] = ['MEMBER', 'CONTRIBUTOR', 'MODERATOR', 'ADMIN'];
const CONTRIBUTING_ROLES: RoleType[] = ['CONTRIBUTOR', 'ADMIN', 'MODERATOR'];

const CommunityHomeHead = ({
  communityId,
  communityName = 'Community Name',
  memberCount = 0,
  avatarUri,
  onConnect,
  onRevokeRequest,
  onDeleteCommunity,
  onAddMembers,
  initialConnectionState,
  role,
  isFromSearch,
  onLeave,
  access,
  isRestricted,
}: CommunityHomeHeadPropsI) => {
  const [connecting, setConnecting] = useState(false);
  const [connectionState, setConnectionState] = useState(initialConnectionState);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalAction, setModalAction] = useState<
    'connect' | 'disconnect' | 'revoke' | 'delete' | 'report' | null
  >(null);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleType>('MEMBER');
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'Community'>>();

  const canCreate = (() => {
    if (access === 'PUBLIC' && !isRestricted) {
      return true;
    }
    if (!role) return false;
    if (access === 'PUBLIC' && isRestricted) {
      return ['MEMBER', 'CONTRIBUTOR', 'MODERATOR', 'ADMIN'].includes(role);
    }
    return ['CONTRIBUTOR', 'MODERATOR', 'ADMIN'].includes(role);
  })();

  useEffect(() => {
    setConnectionState(initialConnectionState);
  }, [initialConnectionState]);

  const onBack = () => {
    if (isFromSearch) navigation.goBack();
    else if (route.params?.fromCommunityTab) {
      navigation.navigate('Forum', { activeTab: 'communities' });
    } else {
      navigation.goBack()
    }
  };

  const handleConnectionAction = async (action: 'connect' | 'disconnect' | 'revoke') => {
    if (connecting) return;

    setConnecting(true);

    try {
      switch (action) {
        case 'connect': {
          setModalAction('connect');
          setIsModalVisible(true);
          break;
        }
        case 'disconnect':
          setModalAction('disconnect');
          setIsModalVisible(true);
          break;
        case 'revoke':
          setModalAction('revoke');
          setIsModalVisible(true);
          break;
      }
    } catch (error) {
      console.error('Connection action failed:', error);
    } finally {
      setConnecting(false);
    }
  };

  const getModalDescription = () => {
    switch (modalAction) {
      case 'disconnect':
        return 'Are you sure you want to leave this community?';
      case 'revoke':
        return 'Are you sure you want to revoke your request to join this community?';
      case 'delete':
        return 'Are you sure you want to delete this community? This action cannot be undone.';
      case 'report':
        return 'Are you sure you want to report this community?';
      case 'connect':
        return 'Choose a role to request while joining this community:';
      default:
        return '';
    }
  };

  const getModalTitle = () => {
    switch (modalAction) {
      case 'delete':
        return 'Delete Community';
      case 'report':
        return 'Report Community';
      case 'connect':
        return 'Join Community';
      default:
        return 'Confirm Action';
    }
  };

  const handleConfirmAction = async () => {
    if (!modalAction) return;

    try {
      if (modalAction === 'delete') {
        await onDeleteCommunity?.();
      } else if (modalAction === 'report') {
      } else if (modalAction === 'connect') {
        await onConnect?.(selectedRole);
        setConnectionState('requested');
      } else if (modalAction === 'disconnect') {
        await onLeave?.();
        setConnectionState('disconnected');
      } else if (modalAction === 'revoke') {
        await onRevokeRequest?.();
        setConnectionState('disconnected');
      }
    } catch (error: any) {
      showToast({
        type: 'error',
        message: 'Failed to complete action',
        description: error?.message,
      });
    } finally {
      setIsModalVisible(false);
      setModalAction(null);
    }
  };

  const RoleSelector = () => (
    <View className="mb-4">
      <View className="flex-row flex-wrap gap-2">
        {ROLES.map((r) => (
          <Pressable
            key={r}
            onPress={() => setSelectedRole(r)}
            className={`px-3 py-2 rounded-lg border ${
              selectedRole === r
                ? 'bg-primaryGreen border-primaryGreen'
                : 'bg-gray-100 border-gray-300'
            }`}
          >
            <Text
              className={`text-sm font-medium ${selectedRole === r ? 'text-white' : 'text-gray-700'}`}
            >
              {r}
            </Text>
          </Pressable>
        ))}
      </View>
    </View>
  );

  const ConnectionButton = () => {
    const buttonConfigs = {
      connected: {
        style: 'bg-white border border-gray-200',
        textStyle: 'text-[#448600]',
        iconColor: '#448600',
        text: 'Joined',
        onPress: () => {},
      },
      requested: {
        style: 'bg-[#448600] border border-[#448600]',
        textStyle: 'text-white',
        iconColor: 'white',
        text: 'Requested',
        onPress: () => handleConnectionAction('revoke'),
      },
      disconnected: {
        style: 'bg-[#448600] border border-[#448600]',
        textStyle: 'text-white',
        iconColor: 'white',
        text: 'Join',
        onPress: () => handleConnectionAction('connect'),
      },
    } as const;

    const config = buttonConfigs[connectionState ?? 'disconnected'];

    return (
      <Pressable
        onPress={config.onPress}
        disabled={connecting}
        className={`flex-row items-center justify-center rounded-lg px-3 py-2 min-w-[80px] ${config.style}`}
        accessibilityLabel={`${config.text} with community`}
        accessibilityRole="button"
      >
        <View className="mr-2">
          {connecting ? (
            <ActivityIndicator size="small" color={config.iconColor} />
          ) : connectionState === 'disconnected' ? (
            <AddConnection color={config.iconColor} width={1.5} height={1.5} />
          ) : (
            <Tick color={config.iconColor} width={1.5} height={1.5} />
          )}
        </View>
        <Text className={`font-medium ${config.textStyle}`}>{config.text}</Text>
      </Pressable>
    );
  };

  const handleDeleteCommunity = async () => {
    try {
      // Expect parent to pass id via route, so navigate to delete via hook action; here we emit event
      // Keeping UI-only placeholder; integration handled in useHook
    } catch (_e) {}
  };

  return (
    <>
      <View className="flex-row items-start justify-between px-4 py-3 bg-white border-b border-borderGrayExtraLight">
        <View className="flex-1 flex-row items-start">
          <BackButton label="" onBack={onBack} />

          <View className="flex-1 flex-row items-start ml-2">
            <UserAvatar avatarUri={avatarUri ?? ''} name={communityName} width={48} height={48} />

            <View className="flex-1 ml-3">
              <Text
                className="text-base font-medium text-labelBlack "
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {communityName}
              </Text>

              <Text className="text-sm text-gray-600 mt-1">{formatMemberCount(memberCount)}</Text>

              <View className="flex-row items-center mt-2 gap-2">
                <ConnectionButton />

                {/* {canCreate ? (
                  <View className="flex-row gap-4">
                    <Pressable
                      onPress={() =>
                        navigation.navigate('CommunityQuestion', { id: 'new', communityId })
                      }
                    >
                      <AddItem width={3.5} height={3.5} />
                    </Pressable>
                  </View>
                ) : (
                  <></>
                )} */}
              </View>
            </View>
          </View>
        </View>

        <Pressable
          className="p-1"
          accessibilityLabel="More options"
          accessibilityRole="button"
          onPress={() => setIsBottomSheetVisible(true)}
        >
          <HorizontalEllipsis width={2} height={2} color="black" />
        </Pressable>
      </View>

      <View>
        <CustomModal
          isVisible={isModalVisible}
          title={getModalTitle()}
          description={getModalDescription()}
          confirmText={
            modalAction === 'connect' ? 'Request' : capitalizeFirstLetter(modalAction ?? '')
          }
          confirmButtonVariant={
            modalAction === 'delete' || modalAction === 'revoke' || modalAction === 'disconnect'
              ? 'danger'
              : 'default'
          }
          cancelText="Cancel"
          onConfirm={handleConfirmAction}
          onCancel={() => {
            setIsModalVisible(false);
            setModalAction(null);
          }}
          isConfirming={connecting}
          bodyComponent={modalAction === 'connect' ? <RoleSelector /> : undefined}
        />
      </View>
      <BottomSheet
        visible={isBottomSheetVisible}
        onClose={() => setIsBottomSheetVisible(false)}
        onModalHide={() => {
          if (modalAction === 'delete' || modalAction === 'disconnect') {
            setIsModalVisible(true);
          }
        }}
      >
        <OptionsMenu>
          {role === 'ADMIN' ? (
            <>
              <OptionItem
                icon={<Settings width={2.5} height={2.5} color="black" />}
                label="Settings"
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  navigation.navigate('ForumSetting', { communityId });
                }}
              />
              <OptionItem
                icon={<AddItem width={2.5} height={2.5} color="black" />}
                label="Add Members"
                onPress={() => {
                  setIsBottomSheetVisible(false);
                  onAddMembers?.();
                }}
              />
              <OptionItem
                icon={<TrashBin width={2.25} height={2.25} color="red" />}
                label="Delete Community"
                textClassName="text-red-500"
                onPress={() => {
                  setModalAction('delete');
                  setIsBottomSheetVisible(false);
                }}
              />
            </>
          ) : null}

          {role ? (
            <OptionItem
              icon={<Logout width={2.25} height={2.25} color="red" />}
              label="Leave Community"
              textClassName="text-red-500"
              onPress={() => {
                setModalAction('disconnect');
                setIsBottomSheetVisible(false);
              }}
            />
          ) : null}

          {role !== 'ADMIN' && (
            <OptionItem
              icon={<ReportFlag color="red" />}
              label="Report"
              textClassName="text-red-500"
              onPress={() => {
                setIsBottomSheetVisible(false);
                setModalAction('report');
                setIsModalVisible(true);
              }}
            />
          )}
          <OptionItem
            icon={<ShareIcon color="black" />}
            label="Share"
            onPress={async () => {
              try {
                const shareUrl = `https://network.navicater.com/community/${communityId ?? ''}`;
                const shareMessage = `🚀 Hey there! I just found an amazing community on Navicater! 🌟 Don't miss out on this awesome community—check it out now and join the conversation! 💡✨ \n\n${shareUrl}\n\n#Navicater #Community`;

                const result = await Share.share({
                  message: shareMessage,
                });

                if (result.action === Share.sharedAction) {
                  showToast({
                    type: 'success',
                    message: 'Shared Successfully!',
                    description: 'Your answer has been shared 🎉',
                  });
                }
              } catch (error) {
                showToast({
                  type: 'error',
                  message: 'Sharing Failed',
                  description: 'Oops! Something went wrong. Try again.',
                });
              } finally {
                setIsBottomSheetVisible(false);
              }
            }}
          />
        </OptionsMenu>
      </BottomSheet>
    </>
  );
};

export default CommunityHomeHead;
