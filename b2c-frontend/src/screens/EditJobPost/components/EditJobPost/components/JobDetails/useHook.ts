import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import { JobDetailsFormDataI } from "./types";
import { createJobStageOneAPI, updateJobDetailsAPI, fetchJobDetailsForEditingAPI } from "@/src/networks/jobs/createJob";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "@/src/redux/store";
import { clearSelection, setSelection } from "@/src/redux/slices/entitysearch/searchSlice";
import { CreateJobStageOneBodyI } from "@/src/networks/jobs/types";
import { showToast } from "@/src/utilities/toast";
import { selectCurrentUser } from "@/src/redux/selectors/user";
import { UserI } from "@/src/redux/slices/content/types";
import { fetchShipDetails } from "@/src/networks/experienceShip.ts/ship";
import { selectCurrentEntityProfile } from "@/src/redux/selectors/entityProfile";

export const useJobDetails = (onNext:(id:string) => void,jobId?:string, editing?: boolean) => {

    const currentUser = useSelector(selectCurrentUser);
    const currentEntity = useSelector(selectCurrentEntityProfile);
    const dispatch = useDispatch<AppDispatch>();
    const [showShipDetails, setShowShipDetails] = useState(false);
    const [isSubmitting,setIsSubmitting] = useState(false);

    const methods = useForm<JobDetailsFormDataI>({
        mode: 'onChange',
        defaultValues: {
          entity: {},
          ship: {},
          shipType: {},
          jobType: '',
          designation: {},
          genderEquityIndex:0,
          expiryDate: '',
          applicationMethod:'IN_APP',
          country:{},
          joiningDate:''
        },
      });
    
    useEffect(() => {
        const fetchJobData = async () => {
            if (!editing) {
                clearFields();
                methods.reset({
                    entity: {},
                    ship: {},
                    shipType: {},
                    jobType: '',
                    designation: {},
                    genderEquityIndex: 0,
                    expiryDate: '',
                    applicationMethod: 'IN_APP',
                    country: {},
                    applicationEmail: '',
                    applicationLink: ''
                });
            } else if (editing && jobId) {
                try {
                    const jobData = await fetchJobDetailsForEditingAPI(jobId);

                    if (jobData) {
                        // Dispatch entity data to Redux store
                        if (jobData.entity && jobData.entity.id) {
                            const entityData = {
                                id: jobData.entity.id,
                                name: jobData.entity.name,
                                dataType: jobData.entity.dataType || 'master'
                            };
                            dispatch(setSelection({ key: 'entity', value: entityData }));
                            methods.setValue('entity', entityData);
                        }

                        // Dispatch ship data to Redux store
                        if (jobData.ship && jobData.ship.imo) {
                            const shipDataForRedux = {
                                id: jobData.ship.imo,  // Redux stores ship with 'id' field
                                name: jobData.ship.name,
                                dataType: jobData.ship.dataType || 'master',
                                imo: jobData.ship.imo   // Also store imo for ImoDataTypeI compatibility
                            };
                            dispatch(setSelection({ key: 'ship', value: shipDataForRedux }));
                            // Note: don't setValue here, let the component's useEffect handle it via Redux
                        }

                        // Dispatch ship type data to Redux store (note: key is 'subVesselType')
                        if (jobData.shipType && jobData.shipType.id) {
                            const shipTypeData = {
                                id: jobData.shipType.id,
                                name: jobData.shipType.name,
                                dataType: jobData.shipType.dataType || 'master'
                            };
                            dispatch(setSelection({ key: 'subVesselType', value: shipTypeData }));
                            methods.setValue('shipType', shipTypeData);
                        }

                        // Dispatch designation data to Redux store
                        if (jobData.designation && jobData.designation.id) {
                            const designationData = {
                                id: jobData.designation.id,
                                name: jobData.designation.name,
                                dataType: jobData.designation.dataType || 'master'
                            };
                            dispatch(setSelection({ key: 'designation', value: designationData }));
                            methods.setValue('designation', designationData);
                        }

                        // Dispatch country data to Redux store
                        if (jobData.countryIso2 && jobData.countryName) {
                            const countryData = {
                                id: jobData.countryIso2,
                                name: jobData.countryName,
                                dataType: 'master' as const
                            };
                            dispatch(setSelection({ key: 'country', value: countryData }));
                            methods.setValue('country', countryData);
                        }

                        // Set job type
                        methods.setValue('jobType', jobData.jobType || '');

                        // Set gender equity index
                        methods.setValue('genderEquityIndex', Number(jobData.genderDiversityIndex) || 0);

                        // Set expiry date - format properly
                        if (jobData.expiryDate) {
                            const date = new Date(jobData.expiryDate);
                            methods.setValue('expiryDate', date.toISOString().split('T')[0]);
                        }

                        // Set application method
                        methods.setValue('applicationMethod', jobData.applicationMethod || 'IN_APP');

                        // Set application email and link
                        methods.setValue('applicationEmail', jobData.applicationEmail || '');
                        methods.setValue('applicationLink', jobData.applicationUrl || '');

                        // Set show ship details flag
                        setShowShipDetails(jobData.showShipDetails || false);
                    }
                } catch (error) {
                    showToast({
                        type: 'error',
                        message: 'Failed to fetch job details'
                    });
                }
            }
        };

        fetchJobData();
    }, [jobId, editing, methods, dispatch])

    useEffect(()=>{
        if(!currentUser.isActive){
            if(currentEntity){
                methods.setValue('entity',{
                    id:currentEntity.entity.id,
                    name:currentEntity.name,
                    dataType:currentEntity.entity.dataType
                })
            }
        }
    },[currentEntity])

    const onSubmit = async () => {
        try{
            setIsSubmitting(true)
            const data = methods.getValues()

            const payload = transformPayload(data,showShipDetails,currentUser as UserI, editing);

            let result;
            if (editing && jobId) {
                result = await updateJobDetailsAPI(jobId, payload, true);
            } else {
                result = await createJobStageOneAPI(payload);
            }
            return result.id
        }catch(e){
            showToast({
                type:'error',
                message: editing ? 'Failed to Update Job Post. Try After sometime' : 'Failed to Save Job Post. Try After sometime'
            })
        }finally{
            setIsSubmitting(false)
        }

    }

    const onGeneratePress = async() => {
        try{
            const isValid = await methods.trigger();

            if (!isValid) {
                showToast({
                    type: 'error',
                    message: 'Please fill all required fields'
                });
                return;
            }
            const jobId = await onSubmit();
            if (jobId) {
                onNext(jobId);
                clearFields()
            }
        }catch(e){
            showToast({
                type: 'error',
                message: 'An error occurred. Please try again.'
            });
        }
    }

    const clearFields = () => {
        dispatch(clearSelection('entity'));
        dispatch(clearSelection('designation'));
        dispatch(clearSelection('ship'));
        dispatch(clearSelection('subVesselType'));
        dispatch(clearSelection('country'));
    };

    const handleToggleShowDetails = () => {
        setShowShipDetails(prev => !prev)
    }

    const populateData = async (imoData: { imo: string; dataType: string }) => {
        try {
          const response = await fetchShipDetails({
            imo: imoData.imo,
            dataType: imoData.dataType,
          });
          if (response.subVesselType.id) {
            methods.setValue('shipType', response.subVesselType);
          }
        } catch (error) {}
      };

    return {
        methods,
        showShipDetails,
        onGeneratePress,
        clearFields,
        setShowShipDetails,
        handleToggleShowDetails,
        populateData
    }
}

const transformPayload = (data:JobDetailsFormDataI,showShipDetails:boolean,currentUser:UserI, isEdit: boolean = false) => {
    // Validate ship details - if showShipDetails is true but ship data is invalid, set it to false
    // Handle both imo and id fields (sometimes ship data comes with id instead of imo)
    const shipId = data.ship?.imo || data.ship?.id;
    const hasValidShipData = Boolean(data.ship && shipId && data.ship.dataType);
    const finalShowShipDetails = Boolean(showShipDetails && hasValidShipData);

    // Build base payload with required fields only
    let payload:CreateJobStageOneBodyI = {
        showShipDetails: finalShowShipDetails,
        isOfficial: currentUser.isActive ? false : true
    };

    // Add entity field if valid (ensure proper IdTypeSchema format)
    if (data.entity?.id && data.entity?.dataType) {
        payload.entity = { id: data.entity.id, dataType: data.entity.dataType };
    }

    // Add designation field if valid (ensure proper IdTypeSchema format)
    if (data.designation?.id && data.designation?.dataType) {
        payload.designation = { id: data.designation.id, dataType: data.designation.dataType };
    }

    // Add ship field if valid (ensure proper ShipImoClientSchema format)
    if (hasValidShipData) {
        payload.ship = { imo: shipId, dataType: data.ship.dataType };
    }

    // Add shipType field if valid (ensure proper IdTypeSchema format)
    if (data.shipType?.id && data.shipType?.dataType) {
        payload.shipType = { id: data.shipType.id, dataType: data.shipType.dataType };
    }

    // Add jobType if valid (ensure proper enum format)
    if (data.jobType) {
        payload.jobType = data.jobType.toUpperCase().replace(/\s+/g, '_');
    }

    // Add expiryDate if valid
    if (data.expiryDate) {
        payload.expiryDate = data.expiryDate;
    }

    // Add genderDiversityIndex if valid (note: backend expects genderDiversityIndex, not genderEquityIndex)
    if (typeof data.genderEquityIndex === 'number') {
        payload.genderDiversityIndex = data.genderEquityIndex;
    }

    // Add applicationMethod if valid (ensure proper enum format)
    if (data.applicationMethod) {
        payload.applicationMethod = data.applicationMethod;

        // Add conditional fields based on application method
        if (data.applicationMethod === 'EMAIL' && data.applicationEmail) {
            payload.applicationEmail = data.applicationEmail;
        } else if (data.applicationMethod === 'EXTERNAL_LINK' && data.applicationLink) {
            payload.applicationUrl = data.applicationLink;
        }
        // Note: IN_APP doesn't need additional fields
    }

    // Add country if valid (backend expects countryIso2 as string, not object)
    if (data.country?.id) {
        payload.countryIso2 = data.country.id;
    }

    if(data.joiningDate) {
        payload.joiningDate = data.joiningDate;
    }

    return payload
}