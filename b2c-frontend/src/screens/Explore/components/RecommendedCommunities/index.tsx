import React, { useCallback } from 'react';
import { FlatList, Text, View, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import NotFound from '@/src/components/NotFound';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommunityItem from '@/src/screens/MyCommunities/components/CommunityItem';
import type { FetchCommunityClientI } from '@/src/networks/community/types';

type RecommendedCommunitiesPropsI = {
  communities: FetchCommunityClientI[];
};

const RecommendedCommunities: React.FC<RecommendedCommunitiesPropsI> = ({ communities }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const renderCommunityItem = useCallback(
    ({ item }: { item: FetchCommunityClientI }) => (
      <CommunityItem
        community={item}
        onPress={() => navigation.navigate('Community', { id: item.id })}
      />
    ),
    [navigation],
  );

  const handleSeeMore = useCallback(() => {
    navigation.navigate('ExploreCommunities');
  }, [navigation]);

  return (
    <View className="px-4">
      <Text className="text-black text-lg font-semibold mb-2">Explore Communities</Text>

      {communities.length === 0 ? (
        <NotFound
          title="No recommendations yet"
          subtitle="Join communities or engage more to get personalized suggestions."
          fullScreen={false}
          className="py-6"
          titleClassName="text-base font-medium"
        />
      ) : (
        <View>
          <FlatList
            data={communities}
            renderItem={renderCommunityItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            className="flex-1"
          />
          {communities.length >= 5 && (
            <Pressable onPress={handleSeeMore} className="mt-4">
              <Text className="text-primaryGreen text-base font-medium">See More</Text>
            </Pressable>
          )}
        </View>
      )}
    </View>
  );
};

export default RecommendedCommunities;
