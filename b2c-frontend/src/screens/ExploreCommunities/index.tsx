
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ExploreListScreen from '@/src/screens/ExploreList';
import CommunityItem from '@/src/screens/MyCommunities/components/CommunityItem';
import { fetchCommunities } from '@/src/networks/community/community';
import { FetchCommunityClientI } from '@/src/networks/community/types';
import { ExploreListConfigI } from '@/src/screens/ExploreList/types';

const ExploreCommunitiesScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleCommunityPress = (community: FetchCommunityClientI) => {
    navigation.navigate('Community', { id: community.id });
  };

  const renderCommunityItem = (item: FetchCommunityClientI) => (
    <CommunityItem
      community={item}
      onPress={() => handleCommunityPress(item)}
    />
  );

  const config: ExploreListConfigI<FetchCommunityClientI> = {
    type: 'communities',
    title: 'Explore Communities',
    fetchData: async ({ page, pageSize }) => {
      const result = await fetchCommunities({
        pageSize: pageSize * (page + 1),
        cursorId: null
      });

      return {
        data: result,
        total: undefined,
      };
    },
    renderItem: renderCommunityItem,
    onItemPress: handleCommunityPress,
    keyExtractor: (item) => item.id,
    groupByAlphabet: true,
    searchEnabled: false,
  };

  return <ExploreListScreen<FetchCommunityClientI> config={config} />;
};

export default ExploreCommunitiesScreen;
