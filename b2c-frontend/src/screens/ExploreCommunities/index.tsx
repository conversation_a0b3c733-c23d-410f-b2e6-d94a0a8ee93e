import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ExploreListScreen from '@/src/screens/ExploreList';
import CommunityItem from '@/src/screens/MyCommunities/components/CommunityItem';
import { fetchCommunities } from '@/src/networks/community/community';
import { FetchCommunityClientI } from '@/src/networks/community/types';
import { ExploreListConfigI } from '@/src/screens/ExploreList/types';

const ExploreCommunitiesScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleCommunityPress = (community: FetchCommunityClientI) => {
    navigation.navigate('Community', { id: community.id });
  };

  const renderCommunityItem = (item: FetchCommunityClientI) => (
    <CommunityItem
      community={item}
      onPress={() => handleCommunityPress(item)}
    />
  );

  const config: ExploreListConfigI = {
    type: 'communities',
    title: 'Explore Communities',
    fetchData: async ({ page, pageSize }) => {
      const result = await fetchCommunities({ 
        pageSize,
        cursorId: page > 0 ? page * pageSize : null 
      });
      return {
        data: result,
        total: undefined, // Backend doesn't return total for communities
      };
    },
    renderItem: renderCommunityItem,
    onItemPress: handleCommunityPress,
    keyExtractor: (item) => (item as FetchCommunityClientI).id,
    groupByAlphabet: true,
    searchEnabled: false,
  };

  return <ExploreListScreen config={config} />;
};

export default ExploreCommunitiesScreen;
