import { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, FlatList, Pressable, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { ExploreListPropsI, ExploreListItemI } from './types';

type GroupedItemsI = { [key: string]: ExploreListItemI[] };

const ALPHABET = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
const PAGE_SIZE = 10;

type FlatListItemI = 
  | { type: 'header'; letter: string }
  | { type: 'item'; item: ExploreListItemI };

const buildFlatData = (availableLetters: string[], groupedItems: GroupedItemsI): FlatListItemI[] => {
  const result: FlatListItemI[] = [];
  availableLetters.forEach((letter) => {
    result.push({ type: 'header', letter });
    groupedItems[letter]?.forEach((item) => {
      result.push({ type: 'item', item });
    });
  });
  return result;
};

const ExploreListScreen: React.FC<ExploreListPropsI> = ({ config }) => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const listRef = useRef<FlatList<any>>(null);

  const [items, setItems] = useState<ExploreListItemI[]>([]);
  const [page, setPage] = useState(0);
  const [total, setTotal] = useState<number | null>(null);
  const [hasMore, setHasMore] = useState(true);

  const [initialLoading, setInitialLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const loadPage = async (nextPage: number, append: boolean) => {
    try {
      append ? setLoadingMore(true) : setInitialLoading(true);
      setError(null);

      const res = await config.fetchData({ page: nextPage, pageSize: PAGE_SIZE });

      const incoming = res.data ?? [];
      if (typeof res.total === 'number') setTotal(res.total);

      setItems((prev) => {
        const base = append ? prev : [];
        const seen = new Set(base.map((item) => config.keyExtractor(item)));
        const merged = [...base];
        for (const item of incoming) {
          const key = config.keyExtractor(item);
          if (!seen.has(key)) {
            seen.add(key);
            merged.push(item);
          }
        }
        return merged;
      });

      const totalSoFar = (append ? items.length : 0) + incoming.length;
      const noMore =
        (typeof res.total === 'number' && totalSoFar >= res.total) || incoming.length === 0;
      setHasMore(!noMore);
      if (!noMore) setPage(nextPage);
    } catch (e) {
      setError(`Failed to load ${config.type}. Please try again.`);
    } finally {
      append ? setLoadingMore(false) : setInitialLoading(false);
    }
  };

  useEffect(() => {
    loadPage(0, false);
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPage(0, false);
    setRefreshing(false);
  };

  const onEndReached = () => {
    if (initialLoading || loadingMore || !hasMore) return;
    loadPage(page + 1, true);
  };

  const groupedItems = useMemo(() => {
    if (!config.groupByAlphabet) return {};
    
    const grouped: GroupedItemsI = {};
    const sorted = [...items].sort((a, b) => {
      const aName = 'name' in a ? a.name : '';
      const bName = 'name' in b ? b.name : '';
      return aName.localeCompare(bName);
    });
    
    sorted.forEach((item) => {
      const name = 'name' in item ? item.name : '';
      const ch = name.charAt(0).toUpperCase();
      if (!grouped[ch]) grouped[ch] = [];
      grouped[ch].push(item);
    });
    return grouped;
  }, [items, config.groupByAlphabet]);

  const availableLetters = useMemo(() => Object.keys(groupedItems).sort(), [groupedItems]);

  const flatListData = useMemo(() => {
    if (!config.groupByAlphabet) {
      return items.map((item) => ({ type: 'item' as const, item }));
    }
    return buildFlatData(availableLetters, groupedItems);
  }, [items, availableLetters, groupedItems, config.groupByAlphabet]);

  const handleLetterPress = (letter: string) => {
    if (!config.groupByAlphabet) return;
    
    setSelectedLetter(letter);
    const flatData = buildFlatData(availableLetters, groupedItems);
    const idx = flatData.findIndex((i) => i.type === 'header' && i.letter === letter);
    if (idx >= 0) {
      listRef.current?.scrollToIndex({ index: idx, animated: true });
    }
  };

  const handleBack = () => navigation.goBack();

  const renderFlatListItem = ({ item: flatItem }: { item: FlatListItemI }) => {
    if (flatItem.type === 'header') {
      return (
        <View className="bg-gray-50 px-5 py-2">
          <Text className="text-lg font-bold text-gray-700">{flatItem.letter}</Text>
        </View>
      );
    }
    return config.renderItem(flatItem.item);
  };

  const renderAlphabetSidebar = () => {
    if (!config.groupByAlphabet) return null;
    
    return (
      <View className="absolute right-2 top-0 bottom-0 justify-center">
        <View className="bg-white rounded-lg shadow-sm px-1 py-2">
          {ALPHABET.map((letter) => {
            const isAvailable = availableLetters.includes(letter);
            const isSelected = selectedLetter === letter;
            return (
              <Pressable
                key={letter}
                onPress={() => isAvailable && handleLetterPress(letter)}
                className={`py-1 px-2 ${isSelected ? 'bg-primaryGreen rounded' : ''}`}
                disabled={!isAvailable}
              >
                <Text
                  className={`text-xs font-medium ${
                    isAvailable
                      ? isSelected
                        ? 'text-white'
                        : 'text-primaryGreen'
                      : 'text-gray-300'
                  }`}
                >
                  {letter}
                </Text>
              </Pressable>
            );
          })}
        </View>
      </View>
    );
  };

  if (error && items.length === 0) {
    return (
      <SafeArea>
        <View className="flex-1 bg-white">
          <View className="flex-row items-center px-4 py-2 border-b border-gray-200">
            <BackButton onBack={handleBack} label="" />
            <Text className="text-2xl font-semibold text-black ml-2">{config.title}</Text>
          </View>
          <View className="flex-1 justify-center items-center px-4">
            <Text className="text-red-500 text-center mb-4">{error}</Text>
            <Pressable
              onPress={() => loadPage(0, false)}
              className="bg-primaryGreen px-6 py-3 rounded-lg"
            >
              <Text className="text-white font-medium">Retry</Text>
            </Pressable>
          </View>
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="flex-row items-center px-4 py-2 border-b border-gray-200">
          <BackButton onBack={handleBack} label="" />
          <Text className="text-2xl font-semibold text-black ml-2">{config.title}</Text>
        </View>

        <View className="flex-1 mb-20">
          <FlatList
            ref={listRef}
            data={flatListData}
            renderItem={renderFlatListItem}
            keyExtractor={(item, index) =>
              item.type === 'header' ? `header-${item.letter}` : `item-${config.keyExtractor(item.item)}-${index}`
            }
            contentContainerStyle={{ paddingBottom: 20, marginBottom: 20 }}
            showsVerticalScrollIndicator={false}
            refreshing={refreshing}
            onRefresh={onRefresh}
            onEndReached={onEndReached}
            onEndReachedThreshold={0.4}
            ListFooterComponent={
              loadingMore ? (
                <View className="py-4 items-center">
                  <ActivityIndicator />
                </View>
              ) : !hasMore ? (
                <View className="py-4 items-center">
                  <Text className="text-gray-400">No more {config.type}</Text>
                </View>
              ) : null
            }
          />

          {renderAlphabetSidebar()}
        </View>
      </View>
    </SafeArea>
  );
};

export default ExploreListScreen;
