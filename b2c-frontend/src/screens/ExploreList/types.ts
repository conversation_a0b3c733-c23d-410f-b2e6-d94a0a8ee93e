import { TopicI } from '@/src/networks/forum/types';
import { FetchCommunityClientI } from '@/src/networks/community/types';

export type ExploreListItemI = TopicI | FetchCommunityClientI;

export type ExploreListTypeI = 'topics' | 'communities';

export type ExploreListConfigI<T = ExploreListItemI> = {
  type: ExploreListTypeI;
  title: string;
  fetchData: (params: { page: number; pageSize: number }) => Promise<{
    data: T[];
    total?: number;
  }>;
  renderItem: (item: T) => React.ReactNode;
  onItemPress: (item: T) => void;
  keyExtractor: (item: T) => string;
  groupByAlphabet?: boolean;
  searchEnabled?: boolean;
};

export type ExploreListPropsI<T = ExploreListItemI> = {
  config: ExploreListConfigI<T>;
};
