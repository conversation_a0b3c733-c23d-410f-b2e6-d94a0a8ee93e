import { TopicI } from '@/src/networks/forum/types';
import { FetchCommunityClientI } from '@/src/networks/community/types';

export type ExploreListItemI = TopicI | FetchCommunityClientI;

export type ExploreListTypeI = 'topics' | 'communities';

export type ExploreListConfigI = {
  type: ExploreListTypeI;
  title: string;
  fetchData: (params: { page: number; pageSize: number }) => Promise<{
    data: ExploreListItemI[];
    total?: number;
  }>;
  renderItem: (item: ExploreListItemI) => React.ReactNode;
  onItemPress: (item: ExploreListItemI) => void;
  keyExtractor: (item: ExploreListItemI) => string;
  groupByAlphabet?: boolean;
  searchEnabled?: boolean;
};

export type ExploreListPropsI = {
  config: ExploreListConfigI;
};
