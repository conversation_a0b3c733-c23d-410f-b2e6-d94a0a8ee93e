// src/screens/ExploreQna/index.tsx
import { Pressable, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import ExploreListScreen from '@/src/screens/ExploreList';
import { fetchTopicsAPI } from '@/src/networks/forum/topics';
import { TopicI } from '@/src/networks/forum/types';
import { ExploreListConfigI } from '@/src/screens/ExploreList/types';

const ExploreQnaScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();

  const handleTopicPress = (topic: TopicI) => {
    navigation.navigate('ExploreForum', {
      type: 'NORMAL',
      topicId: topic.id,
      topicDataType: topic.dataType,
    });
  };

  const renderTopicItem = (item: TopicI) => (
    <Pressable
      className="py-4 px-5 border-b border-gray-100"
      onPress={() => handleTopicPress(item)}
    >
      <Text className="text-lg font-medium text-gray-900">{item.name}</Text>
      <Text className="text-sm text-gray-500 mt-1">
        {item.dataType === 'master' ? 'Verified' : 'Community'}
      </Text>
    </Pressable>
  );

  const config: ExploreListConfigI<TopicI> = {
    type: 'topics',
    title: 'Explore QnA',
    fetchData: async ({ page, pageSize }) => {
      const result = await fetchTopicsAPI({ page, pageSize });
      return {
        data: result.data ?? [],
        total: result.total,
      };
    },
    renderItem: renderTopicItem,
    onItemPress: handleTopicPress,
    keyExtractor: (item) => String(item.id),
    groupByAlphabet: true,
    searchEnabled: false,
  };

  return <ExploreListScreen<TopicI> config={config} />;
};

export default ExploreQnaScreen;