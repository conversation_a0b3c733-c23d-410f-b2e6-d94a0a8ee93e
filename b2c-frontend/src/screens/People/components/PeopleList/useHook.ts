import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { isRejectedWithValue } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import { selectCommunity, selectCommunityLoading } from '@/src/redux/selectors/community';
import { selectProfileId } from '@/src/redux/selectors/user';
import {
  createCommunityAsync,
  setCommunityAvatar,
  setIsLoading,
  setMembers,
  setModerators,
} from '@/src/redux/slices/community/communitySlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { fetchCommunityMemberForClient } from '@/src/networks/community/member';
import {
  fetchCommunityMembersAPI,
  updateCommunityMemberRoleAPI,
} from '@/src/networks/community/members';
import { updateCommunityAPI } from '@/src/networks/community/update';
import { fetchConnectionsAPI } from '@/src/networks/connect/connection';
import { FetchFollowersDataI } from '@/src/networks/connect/types';
import { AuthorProfileI } from '@/src/networks/content/types';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';

const usePeopleList = (
  type: 'create' | 'add',
  searchQuery: string,
  communityId?: string,
  role?: 'ADMIN' | 'MODERATOR',
) => {
  const profileId = useSelector(selectProfileId);
  const [people, setPeople] = useState<FetchFollowersDataI[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loading = useSelector(selectCommunityLoading);
  const [adminIds, setAdminIds] = useState<Set<string>>(new Set());
  const [moderatorIds, setModeratorIds] = useState<Set<string>>(new Set());
  const [roleHolders, setRoleHolders] = useState<FetchFollowersDataI[]>([]);
  const [roleHoldersLoaded, setRoleHoldersLoaded] = useState<boolean>(false);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const communityState = useSelector(selectCommunity);

  const fetchData = async (cursorId?: number | null, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        setIsLoadingMore(true);
      }

      // In role management mode, fetch community members (ALL) and compose list as:
      // [role holders of target role] + [members page excluding role holders]
      if (role && communityId) {
        const res = await fetchCommunityMembersAPI({
          communityId,
          cursorId: cursorId || null,
          pageSize: 10,
          type: 'ALL',
          search: searchQuery?.trim().length ? searchQuery.trim() : undefined,
        });

        const pageItems: FetchFollowersDataI[] = res.data.map((m, idx) => ({
          status: m.role,
          cursorId: 0 + (idx || 0),
          Profile: {
            id: m.Profile.id,
            name: m.Profile.name,
            avatar: m.Profile.avatar,
            designation: m.Profile.designationText
              ? { id: '', name: m.Profile.designationText, dataType: 'master' }
              : null,
            entity: m.Profile.entityText
              ? { id: '', name: m.Profile.entityText, dataType: 'master' }
              : null,
          } as AuthorProfileI,
        }));

        const targetSet = role === 'ADMIN' ? adminIds : moderatorIds;
        const filteredPage = pageItems.filter((p) => !targetSet.has(p.Profile.id));

        const roleTopAll = roleHolders.filter((r) =>
          role === 'ADMIN' ? adminIds.has(r.Profile.id) : moderatorIds.has(r.Profile.id),
        );
        const roleFilteredTop = searchQuery
          ? roleTopAll.filter((r) =>
              r.Profile.name.toLowerCase().includes(searchQuery.toLowerCase()),
            )
          : roleTopAll;

        if (isLoadMore) {
          setPeople((prev) => {
            const seen = new Set(prev.map((p) => p.Profile.id));
            const onlyNew = filteredPage.filter((c) => !seen.has(c.Profile.id));
            return [...prev, ...onlyNew];
          });
        } else {
          const seen = new Set<string>();
          const merged: FetchFollowersDataI[] = [];
          for (const r of roleFilteredTop) {
            if (!seen.has(r.Profile.id)) {
              merged.push(r);
              seen.add(r.Profile.id);
            }
          }
          for (const c of filteredPage) {
            if (!seen.has(c.Profile.id)) {
              merged.push(c);
              seen.add(c.Profile.id);
            }
          }
          setPeople(merged);
        }

        setHasMore(Boolean(res.nextCursorId));
        setNextCursorId(res.nextCursorId || null);
        return;
      }

      const response = await fetchConnectionsAPI({
        profileId,
        cursorId: cursorId || null,
        pageSize: 10,
        name: searchQuery,
      });

      let candidates = response.data;

      if (!role) {
        // Exclude already-selected members from local redux state
        if (type === 'add' && communityId) {
          const existingIds = new Set((communityState.members || []).map((m) => m.id));
          candidates = candidates.filter((c) => !existingIds.has(c.Profile.id));
        }

        // Additionally, exclude members already in the community on server side
        if (type === 'add' && communityId && candidates.length) {
          try {
            const checks = await Promise.allSettled(
              candidates.map(async (item) => {
                const profId = item.Profile.id;
                // Resolves if member exists; rejects if not a member
                await fetchCommunityMemberForClient({ profileId: profId, communityId });
                return profId;
              }),
            );
            const memberIds = new Set(
              checks
                .filter((r): r is PromiseFulfilledResult<string> => r.status === 'fulfilled')
                .map((r) => r.value),
            );
            candidates = candidates.filter((c) => !memberIds.has(c.Profile.id));
          } catch (_e) {
            // Fallback to local-only filter if API check fails
          }
        }
      }

      if (role) {
        const roleFiltered = searchQuery
          ? roleHolders.filter((r) =>
              r.Profile.name.toLowerCase().includes(searchQuery.toLowerCase()),
            )
          : roleHolders;
        if (isLoadMore) {
          // On load more, append only new connections that are not already present
          setPeople((prev) => {
            const seen = new Set(prev.map((p) => p.Profile.id));
            const onlyNew = candidates.filter((c) => !seen.has(c.Profile.id));
            return [...prev, ...onlyNew];
          });
        } else {
          // First page: combine role holders and first page of connections
          const seen = new Set<string>();
          const merged: FetchFollowersDataI[] = [];
          for (const r of roleFiltered) {
            if (!seen.has(r.Profile.id)) {
              merged.push(r);
              seen.add(r.Profile.id);
            }
          }
          for (const c of candidates) {
            if (!seen.has(c.Profile.id)) {
              merged.push(c);
              seen.add(c.Profile.id);
            }
          }
          setPeople(merged);
        }
      } else {
        // Non-role flows: original behavior
        if (isLoadMore) {
          setPeople((prev) => [...prev, ...candidates]);
        } else {
          setPeople(candidates);
        }
      }

      setHasMore(Boolean(response.hasMore));
      setNextCursorId(response.nextCursorId || null);
    } catch (error) {
      console.error('Error fetching people:', error);
    } finally {
      if (isLoadMore) {
        setIsLoadingMore(false);
      }
    }
  };

  const loadMore = () => {
    if (hasMore && !isLoadingMore && nextCursorId) {
      fetchData(nextCursorId, true);
    }
  };

  useEffect(() => {
    try {
      // In role mode, wait until role holders are fetched before composing the list
      if (role && !roleHoldersLoaded) return;
      fetchData(null, false);
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Failed to fetch people',
      });
    }
  }, [searchQuery, role, roleHoldersLoaded]);

  useEffect(() => {
    const fetchAllByRole = async (r: 'ADMIN' | 'MODERATOR') => {
      let cursorId: number | null = null;
      const items: { Profile: any }[] = [];
      do {
        const res = await fetchCommunityMembersAPI({
          communityId: communityId!,
          cursorId,
          pageSize: 10,
          type: r,
        });
        items.push(...res.data.map((d) => ({ Profile: d.Profile })));
        cursorId = res.nextCursorId ?? null;
      } while (cursorId !== null);
      return items;
    };

    const run = async () => {
      if (!communityId || !role) return;
      setRoleHoldersLoaded(false);
      try {
        const [admins, moderators] = await Promise.all([
          fetchAllByRole('ADMIN'),
          fetchAllByRole('MODERATOR'),
        ]);
        const adminSet = new Set(admins.map((a) => a.Profile.id));
        const moderatorSet = new Set(moderators.map((m) => m.Profile.id));
        setAdminIds(adminSet);
        setModeratorIds(moderatorSet);

        // Build roleHolders list for merging with connections
        const mergedMap = new Map<string, FetchFollowersDataI>();
        const pushItem = (p: any, s: 'ADMIN' | 'MODERATOR') => {
          const profile: AuthorProfileI = {
            id: p.Profile.id,
            name: p.Profile.name,
            avatar: p.Profile.avatar,
            designation: p.Profile.designationText
              ? { id: '', name: p.Profile.designationText, dataType: 'master' }
              : null,
            entity: p.Profile.entityText
              ? { id: '', name: p.Profile.entityText, dataType: 'master' }
              : null,
          };
          mergedMap.set(profile.id, { status: s, cursorId: 0, Profile: profile });
        };
        admins.forEach((p) => pushItem(p, 'ADMIN'));
        moderators.forEach((p) => pushItem(p, 'MODERATOR'));
        setRoleHolders(Array.from(mergedMap.values()));
      } catch (_e) {
        setRoleHolders([]);
      } finally {
        setRoleHoldersLoaded(true);
      }
    };

    run();
  }, [communityId, role]);

  const uploadAvatarAndGetUrl = async (): Promise<string | null> => {
    const avatar = communityState.avatar;

    if (!avatar?.image) {
      return null;
    }

    if (avatar.url) {
      return avatar.url;
    }

    if (avatar.image && !avatar.url) {
      try {
        dispatch(setIsLoading(true));
        let extension = 'jpeg';

        if (avatar.image.mime) {
          extension = avatar.image.mime.split('/')[1];
        } else if (avatar.image.path) {
          const pathParts = avatar.image.path.split('.');
          extension = pathParts.length > 1 ? pathParts[pathParts.length - 1].toLowerCase() : 'jpeg';
        }

        const response = await fetchPresignedUrlAPI([extension], 'AVATAR');

        if (Array.isArray(response) && response.length > 0) {
          const presignedData = response[0];

          const fileToUpload = {
            uri: avatar.image.path,
            type: avatar.image.mime || `image/${extension}`,
            filename: avatar.image.filename || `community-avatar.${extension}`,
          };

          await uploadFileWithPresignedUrl(fileToUpload, presignedData.uploadUrl);
          return presignedData.accessUrl;
        }
      } catch (error) {
        console.error('Error uploading avatar:', error);
        showToast({
          type: 'error',
          message: 'Error uploading avatar.',
          description: 'Please try again',
        });
        dispatch(setIsLoading(false));
        throw error;
      }
    }

    return null;
  };

  const handleSubmit = async () => {
    if (type === 'add' && communityId) {
      try {
        const selected = communityState.members as AuthorProfileI[];
        const members = selected.map((u) => ({ profileId: u.id, type: 'MEMBER' as const }));
        await updateCommunityAPI({ id: communityId }, { members });
        showToast({ type: 'success', message: 'Members added' });
        // Optimistically update local redux members list for instant UI feedback
        dispatch(setMembers([...(communityState.members || []), ...selected]));
        navigation.goBack();
      } catch (_e) {
        showToast({ type: 'error', message: 'Failed to add members' });
      }
      return;
    }
    if (type === 'create') {
      try {
        const avatarUrl = await uploadAvatarAndGetUrl();
        if (avatarUrl) {
          dispatch(setCommunityAvatar(avatarUrl));
        }
        const result = await dispatch(createCommunityAsync({}));
        if (isRejectedWithValue(result)) {
          showToast({
            type: 'error',
            message: 'Failed to create community',
          });
        } else {
          showToast({
            type: 'success',
            message: 'Community created successfully',
          });
          navigation.navigate('Forum',{ activeTab: 'communities' });
        }
      } catch (error) {
        showToast({
          type: 'error',
          message: 'Failed to create community',
        });
      }
    } else if (type === 'add') {
      navigation.navigate('CreateCommunity');
      return;
    }
  };

  const updateCommunityState = (isModeratorTitle: boolean, users: AuthorProfileI[]): void => {
    if (isModeratorTitle) {
      dispatch(setModerators(users));
    } else {
      dispatch(setMembers(users));
    }
  };

  const handleBack = () => {
    if (type === 'create' || Boolean(communityId)) {
      navigation.goBack();
    } else {
      navigation.navigate('CreateCommunity');
    }
  };

  const assignRoles = async (selected: AuthorProfileI[]) => {
    if (!communityId || !role) return;
    if (selected.length === 0) return;
    try {
      const ops = await Promise.allSettled(
        selected.map((u) =>
          updateCommunityMemberRoleAPI({ communityId, profileId: u.id, type: role }),
        ),
      );
      const failures = ops.filter((o) => o.status === 'rejected');
      if (failures.length > 0) {
        showToast({
          type: 'error',
          message: `Failed to add ${failures.length} ${role.toLowerCase()}(s)`,
        });
      }
      const successes = ops.filter((o) => o.status === 'fulfilled');
      if (successes.length > 0) {
        showToast({
          type: 'success',
          message: `Added ${successes.length} ${role.toLowerCase()}(s)`,
        });
      }
      // Update local role holder sets so UI reflects new roles
      if (role === 'ADMIN') {
        setAdminIds((prev) => new Set([...Array.from(prev), ...selected.map((s) => s.id)]));
      } else if (role === 'MODERATOR') {
        setModeratorIds((prev) => new Set([...Array.from(prev), ...selected.map((s) => s.id)]));
      }
      navigation.goBack();
    } catch (_e) {
      showToast({ type: 'error', message: `Failed to add ${role.toLowerCase()}s` });
    }
  };

  const disabledRoleFor = (userId: string): 'ADMIN' | 'MODERATOR' | null => {
    if (!role) return null;
    // Always disable if they already hold the target role
    if (role === 'ADMIN' && adminIds.has(userId)) return 'ADMIN';
    if (role === 'MODERATOR' && moderatorIds.has(userId)) return 'MODERATOR';
    // Cross-role exclusion: when adding moderator, disable if ADMIN
    if (role === 'MODERATOR' && adminIds.has(userId)) return 'ADMIN';
    // If you also want to prevent moderators becoming admins, uncomment next line
    // if (role === 'ADMIN' && moderatorIds.has(userId)) return 'MODERATOR';
    return null;
  };

  return {
    handleSubmit,
    people,
    loading,
    isLoadingMore,
    loadMore,
    updateCommunityState,
    handleBack,
    assignRoles,
    disabledRoleFor,
  };
};

export default usePeopleList;
